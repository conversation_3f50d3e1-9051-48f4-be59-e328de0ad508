const { chromium } = require('playwright');
const SmartSelectorManager = require('../core/SmartSelectorManager');

class CommentAutomation {
    constructor(dbManager) {
        this.dbManager = dbManager;
        this.smartSelector = new SmartSelectorManager(dbManager);
        this.browser = null;
        this.context = null;
        this.page = null;
        this.isRunning = false;
        
        // <PERSON><PERSON><PERSON> comments
        this.defaultComments = [
            "Great post! 👍",
            "Thanks for sharing! 🙏",
            "Interesting! 🤔",
            "Love this! ❤️",
            "Amazing! 🔥",
            "So true! 💯",
            "Awesome! 🎉",
            "Nice! 👌",
            "Perfect! ✨",
            "Exactly! 🎯"
        ];
    }

    async initialize(profileId) {
        try {
            console.log(`🚀 Initializing Comment automation for profile: ${profileId}`);
            
            // Get profile data
            const profile = await this.dbManager.getProfile(profileId);
            if (!profile) {
                throw new Error(`Profile ${profileId} not found`);
            }

            // Launch browser with profile
            const profilePath = `${process.env.PROFILES_DIR}/${profileId}`;
            
            this.browser = await chromium.launchPersistentContext(profilePath, {
                headless: false,
                viewport: { width: 1366, height: 768 },
                userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu'
                ]
            });

            this.page = await this.browser.newPage();
            
            // Navigate to Facebook
            await this.page.goto('https://www.facebook.com', { waitUntil: 'networkidle' });
            
            console.log(`✅ Comment automation initialized for profile: ${profileId}`);
            return { success: true };
            
        } catch (error) {
            console.error('❌ Failed to initialize Comment automation:', error);
            await this.cleanup();
            return { success: false, error: error.message };
        }
    }

    async run(options = {}) {
        if (this.isRunning) {
            return { success: false, error: 'Comment automation is already running' };
        }

        this.isRunning = true;
        
        try {
            console.log('🚀 Starting Comment automation...');
            
            const {
                maxComments = 5,
                delayBetweenComments = 60000, // 60 seconds
                customComments = [],
                targetPosts = 'feed'
            } = options;

            const comments = customComments.length > 0 ? customComments : this.defaultComments;
            
            const results = {
                totalComments: 0,
                successfulComments: 0,
                failedComments: 0,
                errors: []
            };

            // Wait for page to load
            await this.page.waitForTimeout(3000);

            // Comment selectors based on inspect results
            const commentSelectors = [
                '[aria-label*="Write a comment"]',
                '[placeholder*="Write a comment"]',
                'div[contenteditable="true"][data-testid*="comment"]',
                'div[role="textbox"][aria-label*="comment"]'
            ];

            for (let i = 0; i < maxComments && this.isRunning; i++) {
                try {
                    console.log(`💬 Attempting comment ${i + 1}/${maxComments}`);
                    
                    // Find comment box using smart selector
                    let commentBox = null;
                    for (const selector of commentSelectors) {
                        try {
                            commentBox = await this.page.$(selector);
                            if (commentBox) {
                                const isVisible = await commentBox.isVisible();
                                if (isVisible) break;
                                commentBox = null;
                            }
                        } catch (error) {
                            continue;
                        }
                    }

                    if (commentBox) {
                        // Scroll to comment box
                        await commentBox.scrollIntoViewIfNeeded();
                        await this.page.waitForTimeout(1000);
                        
                        // Click comment box to focus
                        await commentBox.click();
                        await this.page.waitForTimeout(500);
                        
                        // Get random comment
                        const randomComment = comments[Math.floor(Math.random() * comments.length)];
                        
                        // Type comment
                        await commentBox.fill(randomComment);
                        await this.page.waitForTimeout(1000);
                        
                        // Press Enter to submit
                        await this.page.keyboard.press('Enter');
                        
                        results.successfulComments++;
                        console.log(`✅ Successfully posted comment ${i + 1}: "${randomComment}"`);
                        
                        // Log the action
                        await this.dbManager.addLog({
                            profileId: 'current',
                            action: 'post_comment',
                            target: 'facebook_post',
                            status: 'success',
                            message: `Successfully posted comment: "${randomComment}"`
                        });
                        
                    } else {
                        console.log(`⚠️ No comment box found for post ${i + 1}`);
                        results.failedComments++;
                        
                        // Scroll down to find more posts
                        await this.page.evaluate(() => {
                            window.scrollBy(0, 500);
                        });
                        await this.page.waitForTimeout(2000);
                    }
                    
                    // Wait between comments
                    if (i < maxComments - 1) {
                        console.log(`⏳ Waiting ${delayBetweenComments/1000} seconds before next comment...`);
                        await this.page.waitForTimeout(delayBetweenComments);
                    }
                    
                } catch (error) {
                    console.error(`❌ Error posting comment ${i + 1}:`, error);
                    results.failedComments++;
                    results.errors.push(error.message);
                    
                    await this.dbManager.addLog({
                        profileId: 'current',
                        action: 'post_comment',
                        target: 'facebook_post',
                        status: 'error',
                        message: `Failed to post comment ${i + 1}: ${error.message}`
                    });
                }
            }

            results.totalComments = results.successfulComments + results.failedComments;
            
            console.log(`🎉 Comment automation completed: ${results.successfulComments}/${results.totalComments} successful`);
            
            return {
                success: true,
                results
            };
            
        } catch (error) {
            console.error('❌ Comment automation failed:', error);
            return { success: false, error: error.message };
        } finally {
            this.isRunning = false;
        }
    }

    async stop() {
        console.log('🛑 Stopping Comment automation...');
        this.isRunning = false;
        await this.cleanup();
        return { success: true };
    }

    async cleanup() {
        try {
            if (this.page) {
                await this.page.close();
                this.page = null;
            }
            if (this.browser) {
                await this.browser.close();
                this.browser = null;
            }
            console.log('🧹 Comment automation cleanup completed');
        } catch (error) {
            console.error('❌ Error during Comment automation cleanup:', error);
        }
    }
}

module.exports = CommentAutomation;
