const { contextBridge, ipc<PERSON>enderer } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
    // Profile management
    profiles: {
        getAll: () => ipcRenderer.invoke('profiles:getAll'),
        create: (profileData) => ipcRenderer.invoke('profiles:create', profileData),
        update: (profileId, profileData) => ipcRenderer.invoke('profiles:update', profileId, profileData),
        delete: (profileId) => ipcRenderer.invoke('profiles:delete', profileId),
        test: (profileId) => ipcRenderer.invoke('profiles:test', profileId),
        launchBrowser: (profileId) => ipcRenderer.invoke('profiles:launchBrowser', profileId),
        updateCategory: (profileId, categoryId) => ipcRenderer.invoke('profiles:updateCategory', profileId, categoryId),
        getByCategory: (categoryId) => ipcRenderer.invoke('profiles:getByCategory', categoryId)
    },

    // Category management
    categories: {
        getAll: () => ipcRenderer.invoke('categories:getAll'),
        create: (categoryData) => ipcRenderer.invoke('categories:create', categoryData),
        update: (categoryId, categoryData) => ipcRenderer.invoke('categories:update', categoryId, categoryData),
        delete: (categoryId) => ipcRenderer.invoke('categories:delete', categoryId),
        getStats: () => ipcRenderer.invoke('categories:getStats'),
        getProfiles: (categoryId) => ipcRenderer.invoke('categories:getProfiles', categoryId)
    },

    // Browser window management
    browser: {
        launchWindow: (options) => ipcRenderer.invoke('browser:launchWindow', options),
        closeWindow: (windowId) => ipcRenderer.invoke('browser:closeWindow', windowId),
        getActiveWindows: () => ipcRenderer.invoke('browser:getActiveWindows'),
        getWindowCount: () => ipcRenderer.invoke('browser:getWindowCount'),
        forceCleanup: () => ipcRenderer.invoke('browser:forceCleanup')
    },

    // Automation control
    automation: {
        start: (campaignConfig) => ipcRenderer.invoke('automation:start', campaignConfig),
        stop: (campaignId) => ipcRenderer.invoke('automation:stop', campaignId),
        getStatus: (campaignId) => ipcRenderer.invoke('automation:getStatus', campaignId),

        // Listen for real-time updates
        onProgress: (callback) => {
            ipcRenderer.on('automation:progress', callback);
            return () => ipcRenderer.removeListener('automation:progress', callback);
        },
        onStatusChange: (callback) => {
            ipcRenderer.on('automation:statusChange', callback);
            return () => ipcRenderer.removeListener('automation:statusChange', callback);
        },
        onError: (callback) => {
            ipcRenderer.on('automation:error', callback);
            return () => ipcRenderer.removeListener('automation:error', callback);
        },

        // Enhanced automation methods
        startGroupSharing: (options) => ipcRenderer.invoke('automation:startGroupSharing', options),
        getActiveAutomations: () => ipcRenderer.invoke('automation:getActiveAutomations'),
        getSelectorStats: () => ipcRenderer.invoke('automation:getSelectorStats'),
        stopAutomation: (automationId) => ipcRenderer.invoke('automation:stopAutomation', automationId),

        // Direct automation methods
        runLike: (options) => ipcRenderer.invoke('automation:runLike', options),
        runComment: (options) => ipcRenderer.invoke('automation:runComment', options),
        runShareTimeline: (options) => ipcRenderer.invoke('automation:runShareTimeline', options),
        runShareGroups: (options) => ipcRenderer.invoke('automation:runShareGroups', options)
    },

    // Scheduling management
    schedules: {
        getAll: () => ipcRenderer.invoke('schedules:getAll'),
        create: (scheduleConfig) => ipcRenderer.invoke('schedules:create', scheduleConfig),
        update: (scheduleId, updates) => ipcRenderer.invoke('schedules:update', scheduleId, updates),
        delete: (scheduleId) => ipcRenderer.invoke('schedules:delete', scheduleId),
        start: (scheduleId) => ipcRenderer.invoke('schedules:start', scheduleId),
        stop: (scheduleId) => ipcRenderer.invoke('schedules:stop', scheduleId)
    },

    // Proxy management
    proxies: {
        getAll: () => ipcRenderer.invoke('proxies:getAll'),
        create: (proxyData) => ipcRenderer.invoke('proxies:create', proxyData),
        update: (proxyId, proxyData) => ipcRenderer.invoke('proxies:update', proxyId, proxyData),
        delete: (proxyId) => ipcRenderer.invoke('proxies:delete', proxyId)
    },

    // Logs
    logs: {
        getAll: (filters) => ipcRenderer.invoke('logs:getAll', filters),
        clear: () => ipcRenderer.invoke('logs:clear'),

        // Listen for new logs
        onNewLog: (callback) => {
            ipcRenderer.on('logs:new', callback);
            return () => ipcRenderer.removeListener('logs:new', callback);
        }
    },

    // Settings
    settings: {
        get: () => ipcRenderer.invoke('settings:get'),
        update: (settings) => ipcRenderer.invoke('settings:update', settings),
        getScheduleSettings: () => ipcRenderer.invoke('settings:getScheduleSettings'),
        saveScheduleSettings: (settings) => ipcRenderer.invoke('settings:saveScheduleSettings', settings),
        saveAutomationSettings: (settings) => ipcRenderer.invoke('settings:saveAutomationSettings', settings),
        saveBrowserSettings: (settings) => ipcRenderer.invoke('settings:saveBrowserSettings', settings)
    },

    // File operations
    file: {
        selectFolder: () => ipcRenderer.invoke('file:selectFolder'),
        selectFile: (filters) => ipcRenderer.invoke('file:selectFile', filters),
        selectSaveFile: (options) => ipcRenderer.invoke('file:selectSaveFile', options)
    },

    // Import/Export operations
    importExport: {
        exportToRAR: (profileIds, exportPath) => ipcRenderer.invoke('importExport:exportToRAR', profileIds, exportPath),
        importFromRAR: (rarPath, options) => ipcRenderer.invoke('importExport:importFromRAR', rarPath, options)
    },

    // Utility functions
    utils: {
        generateId: () => ipcRenderer.invoke('utils:generateId'),
        // Remove all listeners for cleanup
        removeAllListeners: () => {
            ipcRenderer.removeAllListeners('automation:progress');
            ipcRenderer.removeAllListeners('automation:statusChange');
            ipcRenderer.removeAllListeners('automation:error');
            ipcRenderer.removeAllListeners('logs:new');
        }
    }
});

// Expose version info
contextBridge.exposeInMainWorld('appInfo', {
    version: process.env.npm_package_version || '1.0.0',
    platform: process.platform,
    arch: process.arch
});
