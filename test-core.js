#!/usr/bin/env node

// Simple test script to verify core functionality without Electron dependencies
const fs = require('fs').promises;
const path = require('path');

// Mock better-sqlite3 if not available
let Database;
try {
    Database = require('better-sqlite3');
} catch (error) {
    console.log('⚠️  better-sqlite3 not available, using mock database');
    Database = class MockDatabase {
        constructor(path) {
            this.path = path;
            this.data = new Map();
        }
        
        prepare(sql) {
            return {
                run: (...params) => ({ lastInsertRowid: Date.now(), changes: 1 }),
                get: (...params) => null,
                all: (...params) => []
            };
        }
        
        exec(sql) {
            console.log(`Mock SQL: ${sql.substring(0, 50)}...`);
        }
        
        pragma(setting) {
            console.log(`Mock PRAGMA: ${setting}`);
        }
        
        close() {
            console.log('Mock database closed');
        }
    };
}

// Mock uuid if not available
let uuidv4;
try {
    uuidv4 = require('uuid').v4;
} catch (error) {
    console.log('⚠️  uuid not available, using mock uuid');
    uuidv4 = () => 'mock-uuid-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
}

// Simple DatabaseManager for testing
class SimpleDatabaseManager {
    constructor() {
        this.db = null;
        this.dbPath = path.join(process.cwd(), 'data', 'facebot.db');
    }

    async initialize() {
        try {
            // Ensure data directory exists
            const dataDir = path.dirname(this.dbPath);
            await fs.mkdir(dataDir, { recursive: true });

            // Open database connection
            this.db = new Database(this.dbPath);
            
            // Enable foreign keys
            this.db.pragma('foreign_keys = ON');
            
            // Create tables
            await this.createTables();
            
            console.log('✅ Database initialized successfully');
            return true;
        } catch (error) {
            console.error('❌ Database initialization failed:', error.message);
            return false;
        }
    }

    async createTables() {
        const tables = [
            `CREATE TABLE IF NOT EXISTS profiles (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                email TEXT,
                password TEXT,
                userDataDir TEXT,
                proxyId TEXT,
                isActive BOOLEAN DEFAULT 1,
                lastUsed INTEGER,
                cooldownExpiresAt INTEGER,
                createdAt INTEGER,
                facebook TEXT
            )`,
            
            `CREATE TABLE IF NOT EXISTS settings (
                key TEXT PRIMARY KEY,
                value TEXT,
                updatedAt INTEGER
            )`,
            
            `CREATE TABLE IF NOT EXISTS automation_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                profileId TEXT,
                action TEXT,
                target TEXT,
                status TEXT,
                message TEXT,
                timestamp INTEGER,
                campaignId TEXT
            )`
        ];

        for (const table of tables) {
            this.db.exec(table);
        }

        // Insert default settings
        const stmt = this.db.prepare('INSERT OR IGNORE INTO settings (key, value, updatedAt) VALUES (?, ?, ?)');
        const defaultSettings = {
            'automation.delay': '10',
            'automation.delayComment': '4',
            'automation.delayShare': '7',
            'automation.batchSize': '5'
        };

        for (const [key, value] of Object.entries(defaultSettings)) {
            stmt.run(key, value, Date.now());
        }
    }

    createProfile(profileData) {
        const id = uuidv4();
        const now = Date.now();
        
        const stmt = this.db.prepare(`
            INSERT INTO profiles (id, name, email, password, userDataDir, isActive, createdAt, facebook) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `);
        
        stmt.run(
            id,
            profileData.name,
            profileData.email,
            profileData.password,
            profileData.userDataDir,
            profileData.isActive !== false,
            now,
            JSON.stringify(profileData.facebook || {})
        );

        return { id, ...profileData, createdAt: now };
    }

    getAllProfiles() {
        const stmt = this.db.prepare('SELECT * FROM profiles ORDER BY createdAt DESC');
        const profiles = stmt.all();
        return profiles.map(profile => {
            if (profile.facebook) {
                try {
                    profile.facebook = JSON.parse(profile.facebook);
                } catch (error) {
                    profile.facebook = {};
                }
            }
            return profile;
        });
    }

    getSettings() {
        const stmt = this.db.prepare('SELECT key, value FROM settings');
        const rows = stmt.all();
        const settings = {};
        rows.forEach(row => {
            settings[row.key] = row.value;
        });
        return settings;
    }

    addLog(logData) {
        const stmt = this.db.prepare(`
            INSERT INTO automation_logs (profileId, action, target, status, message, timestamp, campaignId) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
        `);
        
        stmt.run(
            logData.profileId,
            logData.action,
            logData.target,
            logData.status,
            logData.message,
            Date.now(),
            logData.campaignId || null
        );
    }

    close() {
        if (this.db) {
            this.db.close();
        }
    }
}

// Simple ProfileManager for testing
class SimpleProfileManager {
    constructor(dbManager) {
        this.dbManager = dbManager;
        this.profilesDir = path.join(process.cwd(), 'profiles');
    }

    async initialize() {
        try {
            await fs.mkdir(this.profilesDir, { recursive: true });
            console.log('✅ ProfileManager initialized successfully');
            return true;
        } catch (error) {
            console.error('❌ ProfileManager initialization failed:', error.message);
            return false;
        }
    }

    async createProfile(profileData) {
        try {
            const profileId = uuidv4();
            const userDataDir = path.join(this.profilesDir, profileId);
            
            await fs.mkdir(userDataDir, { recursive: true });
            
            const profile = {
                id: profileId,
                name: profileData.name,
                email: profileData.email,
                password: profileData.password,
                userDataDir: userDataDir,
                isActive: profileData.isActive !== false,
                facebook: {
                    comments: profileData.facebook?.comments || [],
                    shares: profileData.facebook?.shares || [],
                    settings: {
                        delayComment: 4,
                        delayShare: 7,
                        enableComments: true,
                        enableShares: true
                    }
                }
            };
            
            const createdProfile = this.dbManager.createProfile(profile);
            console.log(`✅ Profile created: ${profile.name} (${profileId})`);
            return createdProfile;
        } catch (error) {
            console.error('❌ Failed to create profile:', error.message);
            throw error;
        }
    }

    getAllProfiles() {
        return this.dbManager.getAllProfiles();
    }
}

// Test runner
class CoreTester {
    constructor() {
        this.dbManager = null;
        this.profileManager = null;
    }

    async runTests() {
        console.log('🧪 Running FaceBot Multi Core Tests...\n');
        
        try {
            await this.testDatabase();
            await this.testProfileManager();
            await this.testMigrationLogic();
            await this.createSampleData();
            
            console.log('\n✅ All core tests passed!');
            console.log('\n📋 Core functionality verified:');
            console.log('   • Database operations working');
            console.log('   • Profile management working');
            console.log('   • Migration logic working');
            console.log('   • Sample data creation working');
            console.log('\n🚀 Ready for full application setup!');
            
        } catch (error) {
            console.error('\n❌ Core test failed:', error.message);
            console.error('Stack trace:', error.stack);
            process.exit(1);
        } finally {
            if (this.dbManager) {
                this.dbManager.close();
            }
        }
    }

    async testDatabase() {
        console.log('🔍 Testing database operations...');
        
        this.dbManager = new SimpleDatabaseManager();
        const initialized = await this.dbManager.initialize();
        
        if (!initialized) {
            throw new Error('Database initialization failed');
        }
        
        // Test settings
        const settings = this.dbManager.getSettings();
        console.log(`   ✅ Settings loaded: ${Object.keys(settings).length} entries`);
        
        // Test logging
        this.dbManager.addLog({
            profileId: 'test',
            action: 'test',
            target: 'test',
            status: 'success',
            message: 'Test log entry'
        });
        console.log('   ✅ Logging works');
    }

    async testProfileManager() {
        console.log('\n👥 Testing profile management...');
        
        this.profileManager = new SimpleProfileManager(this.dbManager);
        const initialized = await this.profileManager.initialize();
        
        if (!initialized) {
            throw new Error('ProfileManager initialization failed');
        }
        
        // Test profile creation
        const testProfile = {
            name: 'Test Profile',
            email: '<EMAIL>',
            password: 'testpassword',
            facebook: {
                comments: ['Test comment 1', 'Test comment 2'],
                shares: ['Test share 1', 'Test share 2']
            }
        };
        
        const createdProfile = await this.profileManager.createProfile(testProfile);
        console.log('   ✅ Profile creation works');
        
        // Test profile retrieval
        const profiles = this.profileManager.getAllProfiles();
        console.log(`   ✅ Profile retrieval works: ${profiles.length} profiles found`);
    }

    async testMigrationLogic() {
        console.log('\n🔄 Testing migration logic...');
        
        // Test parsing accounts format
        const accountsData = '<EMAIL>:password1\<EMAIL>:password2';
        const accounts = accountsData.split('\n')
            .filter(line => line.trim() && line.includes(':'))
            .map(line => {
                const [email, password] = line.trim().split(':');
                return { email, password };
            });
        
        console.log(`   ✅ Accounts parsing: ${accounts.length} accounts`);
        
        // Test parsing comments format
        const commentsData = 'Comment 1 :: Comment 2 :: Comment 3';
        const comments = commentsData.split('::')
            .map(comment => comment.trim())
            .filter(comment => comment);
        
        console.log(`   ✅ Comments parsing: ${comments.length} comments`);
        
        // Test creating profiles from migration data
        for (let i = 0; i < Math.min(accounts.length, 2); i++) {
            const account = accounts[i];
            const profileData = {
                name: `Migrated Profile ${i + 1}`,
                email: account.email,
                password: account.password,
                facebook: {
                    comments: comments,
                    shares: []
                }
            };
            
            await this.profileManager.createProfile(profileData);
        }
        
        console.log('   ✅ Migration profile creation works');
    }

    async createSampleData() {
        console.log('\n📝 Creating sample data...');
        
        // Create a few more test profiles
        const sampleProfiles = [
            {
                name: 'Sample Profile 1',
                email: '<EMAIL>',
                password: 'samplepass1',
                facebook: {
                    comments: ['Great post!', 'Thanks for sharing!', 'Interesting!'],
                    shares: ['Sharing this awesome content', 'Check this out!']
                }
            },
            {
                name: 'Sample Profile 2',
                email: '<EMAIL>',
                password: 'samplepass2',
                facebook: {
                    comments: ['Love this!', 'Amazing work', 'Very helpful'],
                    shares: ['Must see this', 'Worth sharing']
                }
            }
        ];
        
        for (const profileData of sampleProfiles) {
            await this.profileManager.createProfile(profileData);
        }
        
        console.log(`   ✅ Created ${sampleProfiles.length} sample profiles`);
        
        // Show final stats
        const allProfiles = this.profileManager.getAllProfiles();
        console.log(`   📊 Total profiles in database: ${allProfiles.length}`);
    }
}

// Run tests if this file is executed directly
if (require.main === module) {
    const tester = new CoreTester();
    tester.runTests().catch(error => {
        console.error('Test runner failed:', error);
        process.exit(1);
    });
}

module.exports = CoreTester;
