const { EventEmitter } = require('events');
// Use real node-cron - no more mocks
const cron = require('node-cron');

class SchedulingManager extends EventEmitter {
    constructor(dbManager, automationController) {
        super();
        this.dbManager = dbManager;
        this.automationController = automationController;
        this.activeSchedules = new Map(); // Map<scheduleId, { task, config }>
        this.scheduledJobs = new Map(); // Map<scheduleId, cronJob>
    }

    /**
     * Create a new scheduled automation
     * @param {object} scheduleConfig - Schedule configuration
     * @returns {Promise<object>} Schedule creation result
     */
    async createSchedule(scheduleConfig) {
        try {
            const {
                name,
                type, // 'group_sharing', 'campaign', 'profile_rotation'
                cronExpression,
                automationConfig,
                isActive = true,
                timezone = 'local'
            } = scheduleConfig;

            // Validate cron expression
            if (!cron.validate(cronExpression)) {
                throw new Error('Invalid cron expression');
            }

            // Generate schedule ID
            const scheduleId = this.generateScheduleId();

            // Create schedule in database
            const schedule = await this.dbManager.saveSchedule({
                id: scheduleId,
                name,
                type,
                cronExpression,
                automationConfig,
                isActive,
                timezone,
                createdAt: Date.now(),
                lastRun: null,
                nextRun: this.getNextRunTime(cronExpression),
                runCount: 0,
                successCount: 0,
                failureCount: 0
            });

            // Start the scheduled job if active
            if (isActive) {
                await this.startSchedule(scheduleId);
            }

            console.log(`📅 Schedule created: ${name} (${scheduleId})`);
            return { success: true, scheduleId, schedule };

        } catch (error) {
            console.error('Failed to create schedule:', error.message);
            return { success: false, error: error.message };
        }
    }

    /**
     * Start a scheduled job
     * @param {string} scheduleId - Schedule ID
     * @returns {Promise<boolean>} Success status
     */
    async startSchedule(scheduleId) {
        try {
            const schedule = this.dbManager.getSchedule(scheduleId);
            if (!schedule) {
                throw new Error('Schedule not found');
            }

            if (this.scheduledJobs.has(scheduleId)) {
                console.log(`Schedule ${scheduleId} is already running`);
                return true;
            }

            // Create cron job
            const cronJob = cron.schedule(schedule.cronExpression, async () => {
                await this.executeScheduledAutomation(scheduleId);
            }, {
                scheduled: false,
                timezone: schedule.timezone
            });

            // Start the job
            cronJob.start();

            // Store job reference
            this.scheduledJobs.set(scheduleId, cronJob);
            this.activeSchedules.set(scheduleId, {
                config: schedule,
                startedAt: Date.now()
            });

            // Update schedule status
            await this.dbManager.updateSchedule(scheduleId, {
                isActive: true,
                nextRun: this.getNextRunTime(schedule.cronExpression)
            });

            console.log(`▶️ Schedule started: ${schedule.name} (${scheduleId})`);
            this.emit('scheduleStarted', { scheduleId, name: schedule.name });

            return true;
        } catch (error) {
            console.error(`Failed to start schedule ${scheduleId}:`, error.message);
            return false;
        }
    }

    /**
     * Stop a scheduled job
     * @param {string} scheduleId - Schedule ID
     * @returns {Promise<boolean>} Success status
     */
    async stopSchedule(scheduleId) {
        try {
            const cronJob = this.scheduledJobs.get(scheduleId);
            if (cronJob) {
                cronJob.stop();
                cronJob.destroy();
                this.scheduledJobs.delete(scheduleId);
            }

            this.activeSchedules.delete(scheduleId);

            // Update schedule status
            await this.dbManager.updateSchedule(scheduleId, {
                isActive: false
            });

            const schedule = this.dbManager.getSchedule(scheduleId);
            console.log(`⏹️ Schedule stopped: ${schedule?.name} (${scheduleId})`);
            this.emit('scheduleStopped', { scheduleId, name: schedule?.name });

            return true;
        } catch (error) {
            console.error(`Failed to stop schedule ${scheduleId}:`, error.message);
            return false;
        }
    }

    /**
     * Execute scheduled automation
     * @param {string} scheduleId - Schedule ID
     * @returns {Promise<void>}
     */
    async executeScheduledAutomation(scheduleId) {
        try {
            const schedule = this.dbManager.getSchedule(scheduleId);
            if (!schedule) {
                console.error(`Schedule ${scheduleId} not found`);
                return;
            }

            console.log(`🚀 Executing scheduled automation: ${schedule.name}`);

            // Update run statistics
            await this.dbManager.updateSchedule(scheduleId, {
                lastRun: Date.now(),
                nextRun: this.getNextRunTime(schedule.cronExpression),
                runCount: schedule.runCount + 1
            });

            let result;

            // Execute automation based on type
            switch (schedule.type) {
                case 'group_sharing':
                    result = await this.executeGroupSharing(schedule.automationConfig);
                    break;

                case 'campaign':
                    result = await this.executeCampaign(schedule.automationConfig);
                    break;

                case 'profile_rotation':
                    result = await this.executeProfileRotation(schedule.automationConfig);
                    break;

                default:
                    throw new Error(`Unknown automation type: ${schedule.type}`);
            }

            // Update success/failure count
            if (result.success) {
                await this.dbManager.updateSchedule(scheduleId, {
                    successCount: schedule.successCount + 1
                });
                console.log(`✅ Scheduled automation completed: ${schedule.name}`);
                this.emit('scheduleExecuted', { scheduleId, success: true, result });
            } else {
                await this.dbManager.updateSchedule(scheduleId, {
                    failureCount: schedule.failureCount + 1
                });
                console.log(`❌ Scheduled automation failed: ${schedule.name}`);
                this.emit('scheduleExecuted', { scheduleId, success: false, error: result.error });
            }

            // Log execution
            await this.dbManager.saveLog({
                type: 'scheduled_automation',
                scheduleId: scheduleId,
                automationType: schedule.type,
                status: result.success ? 'success' : 'failed',
                details: result,
                timestamp: Date.now()
            });

        } catch (error) {
            console.error(`Scheduled automation execution failed for ${scheduleId}:`, error.message);

            // Update failure count
            const schedule = this.dbManager.getSchedule(scheduleId);
            if (schedule) {
                await this.dbManager.updateSchedule(scheduleId, {
                    failureCount: schedule.failureCount + 1
                });
            }

            this.emit('scheduleExecuted', { scheduleId, success: false, error: error.message });
        }
    }

    /**
     * Execute group sharing automation
     * @param {object} config - Automation configuration
     * @returns {Promise<object>} Execution result
     */
    async executeGroupSharing(config) {
        try {
            return await this.automationController.startGroupSharing(config);
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * Execute campaign automation
     * @param {object} config - Campaign configuration
     * @returns {Promise<object>} Execution result
     */
    async executeCampaign(config) {
        try {
            return await this.automationController.startCampaign(config);
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * Execute profile rotation
     * @param {object} config - Rotation configuration
     * @returns {Promise<object>} Execution result
     */
    async executeProfileRotation(config) {
        try {
            // Implement profile rotation logic
            console.log('🔄 Executing profile rotation...');

            // This would rotate active profiles, manage cooldowns, etc.
            // For now, return success
            return { success: true, message: 'Profile rotation completed' };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * Get all schedules
     * @returns {Array} Array of schedules
     */
    getAllSchedules() {
        return this.dbManager.getAllSchedules();
    }

    /**
     * Get schedule by ID
     * @param {string} scheduleId - Schedule ID
     * @returns {object|null} Schedule or null
     */
    getSchedule(scheduleId) {
        return this.dbManager.getSchedule(scheduleId);
    }

    /**
     * Update schedule
     * @param {string} scheduleId - Schedule ID
     * @param {object} updates - Updates to apply
     * @returns {Promise<object>} Update result
     */
    async updateSchedule(scheduleId, updates) {
        try {
            const schedule = await this.dbManager.updateSchedule(scheduleId, updates);

            // If cron expression or active status changed, restart the job
            if (updates.cronExpression || updates.hasOwnProperty('isActive')) {
                await this.stopSchedule(scheduleId);
                if (updates.isActive !== false) {
                    await this.startSchedule(scheduleId);
                }
            }

            return { success: true, schedule };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * Delete schedule
     * @param {string} scheduleId - Schedule ID
     * @returns {Promise<boolean>} Success status
     */
    async deleteSchedule(scheduleId) {
        try {
            await this.stopSchedule(scheduleId);
            const result = this.dbManager.deleteSchedule(scheduleId);

            console.log(`🗑️ Schedule deleted: ${scheduleId}`);
            this.emit('scheduleDeleted', { scheduleId });

            return result;
        } catch (error) {
            console.error(`Failed to delete schedule ${scheduleId}:`, error.message);
            return false;
        }
    }

    /**
     * Get next run time for cron expression
     * @param {string} cronExpression - Cron expression
     * @returns {number} Next run timestamp
     */
    getNextRunTime(cronExpression) {
        try {
            const task = cron.schedule(cronExpression, () => {}, { scheduled: false });
            const nextDate = task.nextDate();
            task.destroy();
            return nextDate ? nextDate.getTime() : null;
        } catch (error) {
            return null;
        }
    }

    /**
     * Get active schedules
     * @returns {Array} Array of active schedule info
     */
    getActiveSchedules() {
        return Array.from(this.activeSchedules.entries()).map(([scheduleId, data]) => ({
            scheduleId,
            name: data.config.name,
            type: data.config.type,
            nextRun: data.config.nextRun,
            startedAt: data.startedAt
        }));
    }

    /**
     * Generate unique schedule ID
     * @returns {string} Schedule ID
     */
    generateScheduleId() {
        return `schedule-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Cleanup all schedules
     * @returns {Promise<void>}
     */
    async cleanup() {
        console.log('🧹 Cleaning up scheduling manager...');

        // Stop all active schedules
        const scheduleIds = Array.from(this.scheduledJobs.keys());
        await Promise.all(scheduleIds.map(id => this.stopSchedule(id)));

        console.log('✅ Scheduling manager cleanup complete');
    }
}

module.exports = SchedulingManager;
