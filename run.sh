#!/bin/bash

echo "========================================"
echo "   FaceBot Multi - Quick Start Script"
echo "========================================"
echo

echo "[1/4] Testing core functionality..."
node test-core.js
if [ $? -ne 0 ]; then
    echo "ERROR: Core test failed!"
    exit 1
fi

echo
echo "[2/4] Installing missing dependencies..."
npm install better-sqlite3 uuid playwright --no-optional
if [ $? -ne 0 ]; then
    echo "WARNING: Some dependencies may have failed to install"
fi

echo
echo "[3/4] Installing Playwright browsers..."
npx playwright install chromium
if [ $? -ne 0 ]; then
    echo "WARNING: Playwright browser installation failed"
    echo "You may need to install manually later"
fi

echo
echo "[4/4] Starting FaceBot Multi..."
echo
echo "========================================"
echo "   Application is starting..."
echo "   Check the Electron window that opens"
echo "========================================"
echo

npm start
