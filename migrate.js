#!/usr/bin/env node

const fs = require('fs').promises;
const path = require('path');
const DatabaseManager = require('./src/database/DatabaseManager');
const ProfileManager = require('./src/core/ProfileManager');

class FaceBotMigration {
    constructor() {
        this.dbManager = null;
        this.profileManager = null;
    }

    async run() {
        console.log('🔄 Starting FaceBot migration...\n');
        
        try {
            await this.initialize();
            await this.migrateFromOriginalFaceBot();
            
            console.log('\n✅ Migration completed successfully!');
            console.log('\n📋 Migration Summary:');
            console.log(`   • Profiles imported and ready to use`);
            console.log(`   • Comments and shares configured`);
            console.log(`   • Original files preserved as backup`);
            console.log('\n🚀 You can now start the application with "npm start"');
            
        } catch (error) {
            console.error('\n❌ Migration failed:', error.message);
            process.exit(1);
        } finally {
            if (this.dbManager) {
                await this.dbManager.close();
            }
        }
    }

    async initialize() {
        console.log('🔧 Initializing database...');
        
        this.dbManager = new DatabaseManager();
        await this.dbManager.initialize();
        
        this.profileManager = new ProfileManager(this.dbManager);
        await this.profileManager.initialize();
        
        console.log('   ✅ Database initialized');
    }

    async migrateFromOriginalFaceBot() {
        console.log('\n📂 Looking for original FaceBot files...');
        
        const currentDir = process.cwd();
        const accountsFile = path.join(currentDir, 'accounts.txt');
        const commentsFile = path.join(currentDir, 'comments.txt');
        const sharesFile = path.join(currentDir, 'shares.txt');
        
        // Check if files exist
        const filesExist = await this.checkFilesExist([accountsFile, commentsFile, sharesFile]);
        
        if (!filesExist.accounts) {
            console.log('   ⚠️  accounts.txt not found - creating example file');
            await this.createExampleFiles();
            return;
        }
        
        console.log('   ✅ Found original FaceBot files');
        
        // Read and parse files
        const accounts = await this.parseAccountsFile(accountsFile);
        const comments = filesExist.comments ? await this.parseCommentsFile(commentsFile) : [];
        const shares = filesExist.shares ? await this.parseSharesFile(sharesFile) : [];
        
        console.log(`\n📊 Migration data:`)
        console.log(`   • Accounts: ${accounts.length}`);
        console.log(`   • Comments: ${comments.length}`);
        console.log(`   • Shares: ${shares.length}`);
        
        if (accounts.length === 0) {
            throw new Error('No valid accounts found in accounts.txt');
        }
        
        // Create profiles
        console.log('\n👥 Creating profiles...');
        const importedProfiles = [];
        
        for (let i = 0; i < accounts.length; i++) {
            const account = accounts[i];
            
            try {
                const profileData = {
                    name: `Profile ${i + 1} (${account.email})`,
                    email: account.email,
                    password: account.password,
                    facebook: {
                        comments: comments,
                        shares: shares,
                        decoyLinks: [],
                        settings: {
                            delayComment: 4,
                            delayShare: 7,
                            delayLogout: 3,
                            enableComments: comments.length > 0,
                            enableShares: shares.length > 0,
                            enableLikes: false,
                            enableDecoyLinks: false
                        }
                    }
                };
                
                const profile = await this.profileManager.createProfile(profileData);
                importedProfiles.push(profile);
                
                console.log(`   ✅ Created profile: ${profile.name}`);
            } catch (error) {
                console.error(`   ❌ Failed to create profile for ${account.email}: ${error.message}`);
            }
        }
        
        // Create backup of original files
        await this.createBackup([accountsFile, commentsFile, sharesFile]);
        
        console.log(`\n✅ Successfully imported ${importedProfiles.length} profiles`);
    }

    async checkFilesExist(filePaths) {
        const results = {};
        
        for (const filePath of filePaths) {
            const fileName = path.basename(filePath, '.txt');
            try {
                await fs.access(filePath);
                results[fileName] = true;
                console.log(`   ✅ Found: ${path.basename(filePath)}`);
            } catch (error) {
                results[fileName] = false;
                console.log(`   ❌ Missing: ${path.basename(filePath)}`);
            }
        }
        
        return results;
    }

    async parseAccountsFile(filePath) {
        try {
            const content = await fs.readFile(filePath, 'utf8');
            const lines = content.split('\n')
                .map(line => line.trim())
                .filter(line => line && line.includes(':'));
            
            const accounts = [];
            for (const line of lines) {
                const [email, password] = line.split(':');
                if (email && password) {
                    accounts.push({ email: email.trim(), password: password.trim() });
                }
            }
            
            return accounts;
        } catch (error) {
            throw new Error(`Failed to parse accounts file: ${error.message}`);
        }
    }

    async parseCommentsFile(filePath) {
        try {
            const content = await fs.readFile(filePath, 'utf8');
            return content.split('::')
                .map(comment => comment.trim())
                .filter(comment => comment);
        } catch (error) {
            console.warn(`Failed to parse comments file: ${error.message}`);
            return [];
        }
    }

    async parseSharesFile(filePath) {
        try {
            const content = await fs.readFile(filePath, 'utf8');
            return content.split('::')
                .map(share => share.trim())
                .filter(share => share);
        } catch (error) {
            console.warn(`Failed to parse shares file: ${error.message}`);
            return [];
        }
    }

    async createExampleFiles() {
        const exampleAccounts = `USERNAME_OR_EMAIL1:PASSWORD1
USERNAME_OR_EMAIL2:PASSWORD2
USERNAME_OR_EMAIL3:PASSWORD3
USERNAME_OR_EMAIL4:PASSWORD4`;

        const exampleComments = `Great post! ::
Thanks for sharing ::
Interesting content ::
Love this! ::
Amazing work`;

        const exampleShares = `Sharing this awesome content ::
Check this out! ::
Must see this ::
Great stuff here ::
Worth sharing`;

        try {
            await fs.writeFile('accounts.txt', exampleAccounts);
            await fs.writeFile('comments.txt', exampleComments);
            await fs.writeFile('shares.txt', exampleShares);
            
            console.log('   ✅ Created example files:');
            console.log('      • accounts.txt - Add your Facebook credentials');
            console.log('      • comments.txt - Add your comment templates');
            console.log('      • shares.txt - Add your share messages');
            console.log('\n   📝 Edit these files with your data and run migration again');
        } catch (error) {
            throw new Error(`Failed to create example files: ${error.message}`);
        }
    }

    async createBackup(filePaths) {
        console.log('\n💾 Creating backup of original files...');
        
        const backupDir = path.join(process.cwd(), 'backup');
        await fs.mkdir(backupDir, { recursive: true });
        
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        
        for (const filePath of filePaths) {
            try {
                await fs.access(filePath);
                const fileName = path.basename(filePath);
                const backupPath = path.join(backupDir, `${timestamp}_${fileName}`);
                await fs.copyFile(filePath, backupPath);
                console.log(`   ✅ Backed up: ${fileName}`);
            } catch (error) {
                // File doesn't exist, skip backup
            }
        }
    }

    async validateMigration() {
        console.log('\n🔍 Validating migration...');
        
        const profiles = await this.profileManager.getAllProfiles();
        
        if (profiles.length === 0) {
            throw new Error('No profiles were created during migration');
        }
        
        console.log(`   ✅ ${profiles.length} profiles created successfully`);
        
        // Check if profiles have required data
        for (const profile of profiles) {
            if (!profile.email || !profile.password) {
                throw new Error(`Profile ${profile.name} is missing email or password`);
            }
            
            if (!profile.facebook) {
                throw new Error(`Profile ${profile.name} is missing Facebook configuration`);
            }
        }
        
        console.log('   ✅ All profiles have valid configuration');
    }
}

// Command line interface
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.includes('--help') || args.includes('-h')) {
        console.log(`
FaceBot Migration Tool

Usage:
  node migrate.js                 # Migrate from original FaceBot files
  node migrate.js --help          # Show this help message

This tool will:
1. Look for accounts.txt, comments.txt, and shares.txt in the current directory
2. Parse the files and create profiles in the new FaceBot Multi database
3. Create backups of original files
4. Set up default automation settings

File formats:
• accounts.txt: email:password (one per line)
• comments.txt: comment1 :: comment2 :: comment3
• shares.txt: share1 :: share2 :: share3
        `);
        process.exit(0);
    }
    
    const migration = new FaceBotMigration();
    migration.run().catch(error => {
        console.error('Migration failed:', error);
        process.exit(1);
    });
}

module.exports = FaceBotMigration;
