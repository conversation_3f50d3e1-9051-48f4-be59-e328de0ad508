// Mock modules for development when dependencies are not available

// Mock UUID
function generateUUID() {
    return 'mock-uuid-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
}

// Mock Database
class MockDatabase {
    constructor(path) {
        this.path = path;
        this.data = new Map();
        console.log(`📄 Mock Database: ${path}`);
    }
    
    prepare(sql) {
        return {
            run: (...params) => {
                console.log(`🔧 Mock SQL Run: ${sql.substring(0, 50)}...`);
                return { lastInsertRowid: Date.now(), changes: 1 };
            },
            get: (...params) => {
                console.log(`🔧 Mock SQL Get: ${sql.substring(0, 50)}...`);
                return null;
            },
            all: (...params) => {
                console.log(`🔧 Mock SQL All: ${sql.substring(0, 50)}...`);
                return [];
            }
        };
    }
    
    exec(sql) {
        console.log(`🔧 Mock SQL Exec: ${sql.substring(0, 50)}...`);
    }
    
    pragma(setting) {
        console.log(`🔧 Mock PRAGMA: ${setting}`);
    }
    
    close() {
        console.log('🔧 Mock database closed');
    }
}

// Mock Playwright
const mockChromium = {
    launch: async (options) => {
        console.log(`🚀 Mock Chromium launched`);
        console.log(`   Headless: ${options?.headless !== false ? 'true' : 'false'}`);
        console.log(`   UserDataDir: ${options?.userDataDir || 'default'}`);
        if (options?.proxy) {
            console.log(`   Proxy: ${options.proxy.server}`);
        }
        
        return {
            newContext: async (options) => {
                console.log(`🌐 Mock New context created`);
                return {
                    newPage: async () => {
                        console.log(`📄 Mock New page created`);
                        return {
                            url: () => 'https://m.facebook.com/',
                            goto: async (url, options) => {
                                console.log(`🌐 Mock Navigate: ${url}`);
                                await new Promise(resolve => setTimeout(resolve, 1000));
                            },
                            waitForSelector: async (selector, options) => {
                                console.log(`🔍 Mock Wait for: ${selector}`);
                                await new Promise(resolve => setTimeout(resolve, 500));
                            },
                            fill: async (selector, text) => {
                                console.log(`✏️  Mock Fill: ${selector} = ${text.substring(0, 20)}...`);
                                await new Promise(resolve => setTimeout(resolve, 300));
                            },
                            click: async (selector) => {
                                console.log(`👆 Mock Click: ${selector}`);
                                await new Promise(resolve => setTimeout(resolve, 500));
                            },
                            waitForLoadState: async (state) => {
                                console.log(`⏳ Mock Wait for load state: ${state}`);
                                await new Promise(resolve => setTimeout(resolve, 1000));
                            },
                            $: async (selector) => {
                                console.log(`🔍 Mock Query: ${selector}`);
                                return { 
                                    click: async () => console.log(`👆 Mock Element Click: ${selector}`),
                                    textContent: async () => 'Mock Text Content',
                                    getAttribute: async (attr) => 'mock-attribute-value'
                                };
                            },
                            evaluate: async (fn, ...args) => {
                                console.log(`🔧 Mock Evaluate function`);
                                return 'Mock Document Title';
                            },
                            close: async () => {
                                console.log(`❌ Mock Page closed`);
                            },
                            setDefaultTimeout: (timeout) => {
                                console.log(`⏱️  Mock Set timeout: ${timeout}ms`);
                            },
                            setDefaultNavigationTimeout: (timeout) => {
                                console.log(`⏱️  Mock Set navigation timeout: ${timeout}ms`);
                            }
                        };
                    },
                    close: async () => {
                        console.log(`❌ Mock Context closed`);
                    }
                };
            },
            close: async () => {
                console.log(`❌ Mock Browser closed`);
            }
        };
    }
};

module.exports = {
    generateUUID,
    MockDatabase,
    mockChromium
};
