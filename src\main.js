const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const path = require('path');
const isDev = process.env.NODE_ENV === 'development' || process.argv.includes('--dev');

// Enable live reload for development
if (isDev) {
    console.log('🔧 Development mode enabled');
    console.log('   • DevTools will open automatically');
    console.log('   • Console logging enabled');
    console.log('   • Non-headless browser mode');
}

// Import core modules
const DatabaseManager = require('./database/DatabaseManager');
const ProfileManager = require('./core/ProfileManager');
const FacebookBot = require('./core/FacebookBot');
const AutomationController = require('./core/AutomationController');
const BrowserWindowManager = require('./core/BrowserWindowManager');
const ImportExportManager = require('./core/ImportExportManager');
const SchedulingManager = require('./core/SchedulingManager');

class MainApp {
    constructor() {
        this.mainWindow = null;
        this.dbManager = null;
        this.profileManager = null;
        this.automationController = null;
        this.browserWindowManager = null;
        this.importExportManager = null;
        this.schedulingManager = null;
        this.isReady = false;
    }

    async initialize() {
        try {
            console.log('🚀 Initializing FaceBot Multi application...');

            // Initialize database
            this.dbManager = new DatabaseManager();
            console.log('✅ Database initialized');

            // Initialize profile manager
            this.profileManager = new ProfileManager(this.dbManager);
            console.log('✅ Profile manager initialized');

            // Recover profile states on startup
            this.profileManager.recoverProfileStates();

            // Initialize automation controller
            this.automationController = new AutomationController(this.dbManager, this.profileManager);
            console.log('✅ Automation controller initialized');

            // Initialize browser window manager
            this.browserWindowManager = new BrowserWindowManager(this.profileManager, this.dbManager);
            console.log('✅ Browser window manager initialized');

            // Initialize import/export manager
            this.importExportManager = new ImportExportManager(this.profileManager, this.dbManager);
            console.log('✅ Import/Export manager initialized');

            // Initialize scheduling manager
            this.schedulingManager = new SchedulingManager(this.dbManager, this.automationController);
            console.log('✅ Scheduling manager initialized');

            // Force cleanup stuck profiles on startup
            await this.browserWindowManager.forceCleanupStuckProfiles();
            console.log('✅ Startup cleanup completed');

            this.isReady = true;
            console.log('✅ Application initialization complete');
        } catch (error) {
            console.error('❌ Failed to initialize application:', error);
            dialog.showErrorBox('Initialization Error', `Failed to initialize application: ${error.message}`);
        }
    }

    createWindow() {
        this.mainWindow = new BrowserWindow({
            width: 1200,
            height: 800,
            minWidth: 1000,
            minHeight: 600,
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                enableRemoteModule: false,
                preload: path.join(__dirname, 'preload.js')
            },
            icon: path.join(__dirname, '../assets/icon.png'),
            show: false,
            titleBarStyle: 'default'
        });

        // Load the app
        const htmlPath = path.join(__dirname, 'renderer/index.html');
        this.mainWindow.loadFile(htmlPath);

        // Open DevTools in development mode
        if (isDev) {
            this.mainWindow.webContents.openDevTools();
            console.log(`📄 Development mode: Loading ${htmlPath}`);
        }

        // Show window when ready
        this.mainWindow.once('ready-to-show', () => {
            this.mainWindow.show();
        });

        // Handle window closed
        this.mainWindow.on('closed', () => {
            this.mainWindow = null;
        });
    }

    setupIPC() {
        // Profile management IPC handlers
        ipcMain.handle('profiles:getAll', async () => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.profileManager.getAllProfiles();
        });

        ipcMain.handle('profiles:create', async (event, profileData) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.profileManager.createProfile(profileData);
        });

        ipcMain.handle('profiles:update', async (event, profileId, profileData) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.profileManager.updateProfile(profileId, profileData);
        });

        ipcMain.handle('profiles:delete', async (event, profileId) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.profileManager.deleteProfile(profileId);
        });

        ipcMain.handle('profiles:test', async (event, profileId) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.profileManager.testProfile(profileId);
        });

        ipcMain.handle('profiles:launchBrowser', async (event, profileId) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.profileManager.launchBrowserForProfile(profileId, this.browserWindowManager);
        });

        ipcMain.handle('profiles:updateCategory', async (event, profileId, categoryId) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.profileManager.updateProfileCategory(profileId, categoryId);
        });

        ipcMain.handle('profiles:getByCategory', async (event, categoryId) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.profileManager.getProfilesByCategory(categoryId);
        });

        // Automation IPC handlers
        ipcMain.handle('automation:start', async (event, campaignConfig) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.automationController.startCampaign(campaignConfig);
        });

        ipcMain.handle('automation:stop', async (event, campaignId) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.automationController.stopCampaign(campaignId);
        });

        ipcMain.handle('automation:getStatus', async (event, campaignId) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.automationController.getCampaignStatus(campaignId);
        });

        // Enhanced automation IPC handlers
        ipcMain.handle('automation:startGroupSharing', async (event, options) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.automationController.startGroupSharing(options);
        });

        ipcMain.handle('automation:getActiveAutomations', async () => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.automationController.getActiveAutomations();
        });

        ipcMain.handle('automation:getSelectorStats', async () => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.automationController.getSmartSelectorStats();
        });

        ipcMain.handle('automation:stopAutomation', async (event, automationId) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.automationController.stopAutomation(automationId);
        });

        // Simple automation handlers
        ipcMain.handle('automation:runLike', async (event, options = {}) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.automationController.runLikeAutomation(options);
        });

        ipcMain.handle('automation:runComment', async (event, options = {}) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.automationController.runCommentAutomation(options);
        });

        ipcMain.handle('automation:runShareTimeline', async (event, options = {}) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.automationController.runShareTimelineAutomation(options);
        });

        ipcMain.handle('automation:runShareGroups', async (event, options = {}) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.automationController.runShareGroupsAutomation(options);
        });

        // Scheduling IPC handlers
        ipcMain.handle('schedules:getAll', async () => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.schedulingManager.getAllSchedules();
        });

        ipcMain.handle('schedules:create', async (event, scheduleConfig) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.schedulingManager.createSchedule(scheduleConfig);
        });

        ipcMain.handle('schedules:update', async (event, scheduleId, updates) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.schedulingManager.updateSchedule(scheduleId, updates);
        });

        ipcMain.handle('schedules:delete', async (event, scheduleId) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.schedulingManager.deleteSchedule(scheduleId);
        });

        ipcMain.handle('schedules:start', async (event, scheduleId) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.schedulingManager.startSchedule(scheduleId);
        });

        ipcMain.handle('schedules:stop', async (event, scheduleId) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.schedulingManager.stopSchedule(scheduleId);
        });

        // Proxy management IPC handlers
        ipcMain.handle('proxies:getAll', async () => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.getAllProxies();
        });

        ipcMain.handle('proxies:create', async (event, proxyData) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.createProxy(proxyData);
        });

        ipcMain.handle('proxies:update', async (event, proxyId, proxyData) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.updateProxy(proxyId, proxyData);
        });

        ipcMain.handle('proxies:delete', async (event, proxyId) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.deleteProxy(proxyId);
        });

        // Logs IPC handlers
        ipcMain.handle('logs:getAll', async (event, filters) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.getLogs(filters);
        });

        ipcMain.handle('logs:clear', async () => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.clearLogs();
        });

        // Settings IPC handlers
        ipcMain.handle('settings:get', async () => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.getSettings();
        });

        ipcMain.handle('settings:update', async (event, settings) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.updateSettings(settings);
        });

        ipcMain.handle('settings:getScheduleSettings', async () => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.getScheduleSettings();
        });

        ipcMain.handle('settings:saveScheduleSettings', async (event, settings) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.saveScheduleSettings(settings);
        });

        ipcMain.handle('settings:saveAutomationSettings', async (event, settings) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.saveAutomationSettings(settings);
        });

        ipcMain.handle('settings:saveBrowserSettings', async (event, settings) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.saveBrowserSettings(settings);
        });

        // Content Management Handlers
        ipcMain.handle('content:getComments', async (event) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.getComments();
        });

        ipcMain.handle('content:addComment', async (event, text) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.addComment(text);
        });

        ipcMain.handle('content:addBulkComments', async (event, comments) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.addBulkComments(comments);
        });

        ipcMain.handle('content:deleteComment', async (event, index) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.deleteComment(index);
        });

        ipcMain.handle('content:getLinks', async (event) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.getLinks();
        });

        ipcMain.handle('content:addLink', async (event, link) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.addLink(link);
        });

        ipcMain.handle('content:addBulkLinks', async (event, links) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.addBulkLinks(links);
        });

        ipcMain.handle('content:deleteLink', async (event, index) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.deleteLink(index);
        });

        ipcMain.handle('content:getGroupLinks', async (event) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.getGroupLinks();
        });

        ipcMain.handle('content:addGroupLink', async (event, group) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.addGroupLink(group);
        });

        ipcMain.handle('content:addBulkGroupLinks', async (event, groups) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.addBulkGroupLinks(groups);
        });

        ipcMain.handle('content:deleteGroupLink', async (event, index) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.deleteGroupLink(index);
        });

        ipcMain.handle('content:getDecoyLinks', async (event) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.getDecoyLinks();
        });

        ipcMain.handle('content:addDecoyLink', async (event, link) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.addDecoyLink(link);
        });

        ipcMain.handle('content:addBulkDecoyLinks', async (event, links) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.addBulkDecoyLinks(links);
        });

        ipcMain.handle('content:deleteDecoyLink', async (event, index) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.deleteDecoyLink(index);
        });

        // Content Status Update Handlers
        ipcMain.handle('content:updateCommentsStatus', async (event, statusUpdates) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.updateCommentsStatus(statusUpdates);
        });

        ipcMain.handle('content:updateLinksStatus', async (event, statusUpdates) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.updateLinksStatus(statusUpdates);
        });

        ipcMain.handle('content:updateGroupLinksStatus', async (event, statusUpdates) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.updateGroupLinksStatus(statusUpdates);
        });

        ipcMain.handle('content:updateDecoyLinksStatus', async (event, statusUpdates) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.updateDecoyLinksStatus(statusUpdates);
        });

        // Target Automation Settings Handlers
        ipcMain.handle('settings:saveTargetAutomationSettings', async (event, settings) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.saveTargetAutomationSettings(settings);
        });

        ipcMain.handle('settings:getTargetAutomationSettings', async (event) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.getTargetAutomationSettings();
        });

        // Red List Handlers
        ipcMain.handle('redList:getAll', async (event) => {
            if (!this.isReady) throw new Error('Application not ready');
            return this.dbManager.getRedList();
        });

        ipcMain.handle('redList:add', async (event, profileId, reason) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.addToRedList(profileId, reason);
        });

        ipcMain.handle('redList:remove', async (event, profileId) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.removeFromRedList(profileId);
        });

        ipcMain.handle('redList:clear', async (event) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.clearRedList();
        });

        // File operations
        ipcMain.handle('file:selectFolder', async (event, options) => {
            const result = await dialog.showOpenDialog(this.mainWindow, {
                properties: ['openDirectory'],
                title: options?.title || 'Select Folder'
            });
            return result;
        });

        ipcMain.handle('file:selectFile', async (event, options) => {
            const result = await dialog.showOpenDialog(this.mainWindow, {
                properties: ['openFile'],
                filters: options?.filters || [],
                title: options?.title || 'Select File'
            });
            return result;
        });

        ipcMain.handle('file:selectSaveFile', async (event, options) => {
            const result = await dialog.showSaveDialog(this.mainWindow, {
                title: options?.title || 'Save File',
                defaultPath: options?.defaultPath || '',
                filters: options?.filters || []
            });
            return result;
        });

        // Migration IPC handlers
        ipcMain.handle('migration:importFromFacebot', async (event, accountsPath, commentsPath, sharesPath) => {
            if (!this.isReady) throw new Error('Application not ready');
            try {
                const result = await this.profileManager.importProfilesFromFacebot(accountsPath, commentsPath, sharesPath);
                return result;
            } catch (error) {
                console.error('Migration import failed:', error);
                return { success: false, error: error.message };
            }
        });

        // Import/Export RAR IPC handlers
        ipcMain.handle('importExport:exportToRAR', async (event, profileIds, exportPath) => {
            if (!this.isReady) throw new Error('Application not ready');
            try {
                const result = await this.importExportManager.exportProfilesToRAR(profileIds, exportPath);
                return result;
            } catch (error) {
                console.error('Export to RAR failed:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('importExport:importFromRAR', async (event, rarPath, options) => {
            if (!this.isReady) throw new Error('Application not ready');
            try {
                const result = await this.importExportManager.importProfilesFromRAR(rarPath, options);
                return result;
            } catch (error) {
                console.error('Import from RAR failed:', error);
                return { success: false, error: error.message };
            }
        });

        // Categories IPC handlers
        ipcMain.handle('categories:getAll', async () => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.getAllCategories();
        });

        ipcMain.handle('categories:create', async (event, categoryData) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.saveCategory(categoryData);
        });

        ipcMain.handle('categories:update', async (event, categoryId, categoryData) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.updateCategory(categoryId, categoryData);
        });

        ipcMain.handle('categories:delete', async (event, categoryId) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.deleteCategory(categoryId);
        });

        ipcMain.handle('categories:getStats', async () => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.getCategoryStats();
        });

        ipcMain.handle('categories:getProfiles', async (event, categoryId) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.getProfilesByCategory(categoryId);
        });

        // Browser Window Management IPC handlers
        ipcMain.handle('browser:launchWindow', async (event, options) => {
            if (!this.isReady) throw new Error('Application not ready');
            try {
                const result = await this.browserWindowManager.launchNewWindow(options);
                return result;
            } catch (error) {
                console.error('Failed to launch browser window:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('browser:closeWindow', async (event, windowId) => {
            if (!this.isReady) throw new Error('Application not ready');
            try {
                const result = await this.browserWindowManager.closeWindow(windowId);
                return { success: result };
            } catch (error) {
                console.error('Failed to close browser window:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('browser:getActiveWindows', async () => {
            if (!this.isReady) throw new Error('Application not ready');
            return this.browserWindowManager.getActiveWindows();
        });

        ipcMain.handle('browser:getWindowCount', async () => {
            if (!this.isReady) throw new Error('Application not ready');
            return this.browserWindowManager.getActiveWindowCount();
        });

        ipcMain.handle('browser:forceCleanup', async () => {
            if (!this.isReady) throw new Error('Application not ready');
            try {
                await this.browserWindowManager.forceCleanupStuckProfiles();
                await this.browserWindowManager.cleanupDeadProcesses();
                return { success: true };
            } catch (error) {
                console.error('Failed to force cleanup:', error);
                return { success: false, error: error.message };
            }
        });

        // Utility IPC handlers
        ipcMain.handle('utils:generateId', () => {
            // Generate UUID using the same method as ProfileManager
            try {
                const { v4: uuidv4 } = require('uuid');
                return uuidv4();
            } catch (error) {
                // Fallback to mock UUID
                return 'uuid-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
            }
        });
    }
}

// Create app instance
const mainApp = new MainApp();

// App event handlers
app.whenReady().then(async () => {
    await mainApp.initialize();
    mainApp.createWindow();
    mainApp.setupIPC();

    app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
            mainApp.createWindow();
        }
    });
});

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('before-quit', async () => {
    // Cleanup before quitting
    console.log('🔄 Application shutting down, cleaning up...');

    if (mainApp.automationController) {
        await mainApp.automationController.cleanup();
        console.log('✅ Automation controller stopped');
    }

    if (mainApp.schedulingManager) {
        await mainApp.schedulingManager.cleanup();
        console.log('✅ Scheduling manager stopped');
    }

    if (mainApp.browserWindowManager) {
        await mainApp.browserWindowManager.closeAllWindows();
        console.log('✅ All browser windows closed');
    }

    if (mainApp.dbManager) {
        await mainApp.dbManager.close();
        console.log('✅ Database connection closed');
    }

    console.log('✅ Application cleanup complete');
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    dialog.showErrorBox('Unexpected Error', `An unexpected error occurred: ${error.message}`);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
