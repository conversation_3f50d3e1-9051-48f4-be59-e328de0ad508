import React, { useState, useEffect } from 'react';
import { createRoot } from 'react-dom/client';

// Main FaceBot Application Component
function FaceBotApp() {
    const [currentPage, setCurrentPage] = useState('dashboard');
    const [profiles, setProfiles] = useState([]);
    const [campaigns, setCampaigns] = useState([]);
    const [proxies, setProxies] = useState([]);
    const [logs, setLogs] = useState([]);
    const [settings, setSettings] = useState({});
    const [loading, setLoading] = useState(false);
    const [loadingText, setLoadingText] = useState('Loading...');

    // Initialize application
    useEffect(() => {
        initializeApp();
        setupRealTimeUpdates();
        updateClock();
    }, []);

    const initializeApp = async () => {
        try {
            setLoading(true);
            setLoadingText('Initializing application...');
            
            // Load initial data
            await loadInitialData();
            
            setLoading(false);
            showToast('Application initialized successfully', 'success');
        } catch (error) {
            setLoading(false);
            showToast(`Initialization failed: ${error.message}`, 'error');
            console.error('App initialization failed:', error);
        }
    };

    const loadInitialData = async () => {
        try {
            // Load profiles
            const profilesData = await window.electronAPI.profiles.getAll();
            setProfiles(profilesData);

            // Load proxies
            const proxiesData = await window.electronAPI.proxies.getAll();
            setProxies(proxiesData);

            // Load recent logs
            const logsData = await window.electronAPI.logs.getAll({ limit: 50 });
            setLogs(logsData);

            // Load settings
            const settingsData = await window.electronAPI.settings.get();
            setSettings(settingsData);

        } catch (error) {
            console.error('Failed to load initial data:', error);
            throw error;
        }
    };

    const setupRealTimeUpdates = () => {
        // Listen for automation progress updates
        if (window.electronAPI?.automation?.onProgress) {
            window.electronAPI.automation.onProgress((event, data) => {
                handleAutomationProgress(data);
            });
        }

        // Listen for new logs
        if (window.electronAPI?.logs?.onNewLog) {
            window.electronAPI.logs.onNewLog((event, log) => {
                handleNewLog(log);
            });
        }
    };

    const updateClock = () => {
        const updateTime = () => {
            const now = new Date();
            const timeElement = document.getElementById('current-time');
            if (timeElement) {
                timeElement.textContent = now.toLocaleTimeString();
            }
        };
        
        updateTime();
        setInterval(updateTime, 1000);
    };

    const handleAutomationProgress = (data) => {
        console.log('Automation progress:', data);
        // Update UI with progress data
    };

    const handleNewLog = (log) => {
        setLogs(prevLogs => [log, ...prevLogs.slice(0, 99)]);
    };

    const showToast = (message, type = 'info') => {
        const toast = document.createElement('div');
        toast.className = `bg-white border-l-4 p-4 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full`;
        
        const colors = {
            success: 'border-green-500',
            error: 'border-red-500',
            warning: 'border-yellow-500',
            info: 'border-blue-500'
        };
        
        const icons = {
            success: 'fas fa-check-circle text-green-500',
            error: 'fas fa-exclamation-circle text-red-500',
            warning: 'fas fa-exclamation-triangle text-yellow-500',
            info: 'fas fa-info-circle text-blue-500'
        };
        
        toast.classList.add(colors[type]);
        toast.innerHTML = `
            <div class="flex items-center">
                <i class="${icons[type]} mr-3"></i>
                <span>${message}</span>
                <button class="ml-auto text-gray-400 hover:text-gray-600" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        const container = document.getElementById('toast-container');
        if (container) {
            container.appendChild(toast);
            
            // Animate in
            setTimeout(() => {
                toast.classList.remove('translate-x-full');
            }, 100);
            
            // Auto remove after 5 seconds
            setTimeout(() => {
                toast.classList.add('translate-x-full');
                setTimeout(() => toast.remove(), 300);
            }, 5000);
        }
    };

    const testProfile = async (profileId) => {
        setLoading(true);
        setLoadingText('Testing profile...');
        try {
            const result = await window.electronAPI.profiles.test(profileId);
            if (result.success) {
                showToast('Profile test successful', 'success');
            } else {
                showToast(`Profile test failed: ${result.error}`, 'error');
            }
        } catch (error) {
            showToast(`Profile test failed: ${error.message}`, 'error');
        } finally {
            setLoading(false);
        }
    };

    const deleteProfile = async (profileId) => {
        if (confirm('Are you sure you want to delete this profile?')) {
            try {
                await window.electronAPI.profiles.delete(profileId);
                setProfiles(profiles.filter(p => p.id !== profileId));
                showToast('Profile deleted successfully', 'success');
            } catch (error) {
                showToast(`Failed to delete profile: ${error.message}`, 'error');
            }
        }
    };

    const refreshCurrentPage = async () => {
        setLoading(true);
        setLoadingText('Refreshing...');
        try {
            await loadInitialData();
            showToast('Page refreshed successfully', 'success');
        } catch (error) {
            showToast(`Refresh failed: ${error.message}`, 'error');
        } finally {
            setLoading(false);
        }
    };

    const calculateSuccessRate = () => {
        if (logs.length === 0) return 0;
        const successCount = logs.filter(log => log.status === 'success').length;
        return Math.round((successCount / logs.length) * 100);
    };

    const getProfileStatus = (profile) => {
        if (!profile.isActive) return 'Inactive';
        if (profile.cooldownExpiresAt && profile.cooldownExpiresAt > Date.now()) return 'Cooldown';
        return 'Available';
    };

    const renderProfiles = () => {
        if (profiles.length === 0) {
            return (
                <div className="col-span-full text-center py-12">
                    <i className="fas fa-users text-4xl text-gray-300 mb-4"></i>
                    <h3 className="text-lg font-semibold text-gray-600 mb-2">No profiles yet</h3>
                    <p className="text-gray-500 mb-4">Create your first profile to get started</p>
                    <button className="btn-primary text-white py-2 px-4 rounded-lg">
                        <i className="fas fa-plus mr-2"></i>Add Profile
                    </button>
                </div>
            );
        }
        
        return profiles.map(profile => (
            <div key={profile.id} className="card p-6">
                <div className="flex items-center justify-between mb-4">
                    <h4 className="font-semibold">{profile.name}</h4>
                    <span className={`status-indicator ${profile.isActive ? 'status-active' : 'status-inactive'}`}></span>
                </div>
                <div className="space-y-2 text-sm text-gray-600">
                    <div><strong>Email:</strong> {profile.email}</div>
                    <div><strong>Last Used:</strong> {profile.lastUsed ? new Date(profile.lastUsed).toLocaleDateString() : 'Never'}</div>
                    <div><strong>Status:</strong> {getProfileStatus(profile)}</div>
                </div>
                <div className="mt-4 flex space-x-2">
                    <button 
                        className="bg-blue-500 hover:bg-blue-600 text-white py-1 px-3 rounded text-sm"
                        onClick={() => testProfile(profile.id)}
                    >
                        <i className="fas fa-vial mr-1"></i>Test
                    </button>
                    <button className="bg-gray-500 hover:bg-gray-600 text-white py-1 px-3 rounded text-sm">
                        <i className="fas fa-edit mr-1"></i>Edit
                    </button>
                    <button 
                        className="bg-red-500 hover:bg-red-600 text-white py-1 px-3 rounded text-sm"
                        onClick={() => deleteProfile(profile.id)}
                    >
                        <i className="fas fa-trash mr-1"></i>Delete
                    </button>
                </div>
            </div>
        ));
    };

    const renderRecentLogs = () => {
        const recentLogs = logs.slice(0, 5);
        if (recentLogs.length === 0) {
            return <p className="text-gray-500 text-center py-4">No recent activity</p>;
        }
        
        return recentLogs.map((log, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center">
                    <span className={`status-indicator ${log.status === 'success' ? 'status-active' : 'status-inactive'}`}></span>
                    <span className="text-sm">{log.action}: {log.message}</span>
                </div>
                <span className="text-xs text-gray-500">{new Date(log.timestamp).toLocaleTimeString()}</span>
            </div>
        ));
    };

    const renderPageContent = () => {
        switch (currentPage) {
            case 'dashboard':
                return (
                    <div>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                            <div className="card p-6">
                                <div className="flex items-center">
                                    <div className="p-3 rounded-full bg-blue-100 text-blue-600 mr-4">
                                        <i className="fas fa-users"></i>
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Total Profiles</p>
                                        <p className="text-2xl font-semibold">{profiles.length}</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div className="card p-6">
                                <div className="flex items-center">
                                    <div className="p-3 rounded-full bg-green-100 text-green-600 mr-4">
                                        <i className="fas fa-play"></i>
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Active Profiles</p>
                                        <p className="text-2xl font-semibold">{profiles.filter(p => p.isActive).length}</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div className="card p-6">
                                <div className="flex items-center">
                                    <div className="p-3 rounded-full bg-yellow-100 text-yellow-600 mr-4">
                                        <i className="fas fa-check"></i>
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Success Rate</p>
                                        <p className="text-2xl font-semibold">{calculateSuccessRate()}%</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div className="card p-6">
                                <div className="flex items-center">
                                    <div className="p-3 rounded-full bg-purple-100 text-purple-600 mr-4">
                                        <i className="fas fa-network-wired"></i>
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Proxies</p>
                                        <p className="text-2xl font-semibold">{proxies.length}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div className="card p-6">
                            <h3 className="text-lg font-semibold mb-4">Recent Activity</h3>
                            <div className="space-y-3">
                                {renderRecentLogs()}
                            </div>
                        </div>
                    </div>
                );
                
            case 'profiles':
                return (
                    <div>
                        <div className="mb-6 flex justify-between items-center">
                            <div>
                                <h3 className="text-lg font-semibold">Manage Profiles</h3>
                                <p className="text-gray-600">Create and manage your Facebook automation profiles</p>
                            </div>
                            <div className="space-x-2">
                                <button className="btn-primary text-white py-2 px-4 rounded-lg">
                                    <i className="fas fa-plus mr-2"></i>Add Profile
                                </button>
                                <button className="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg">
                                    <i className="fas fa-upload mr-2"></i>Import
                                </button>
                            </div>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {renderProfiles()}
                        </div>
                    </div>
                );
                
            default:
                return (
                    <div className="text-center py-12">
                        <h3 className="text-lg font-semibold mb-2">{currentPage.charAt(0).toUpperCase() + currentPage.slice(1)} page</h3>
                        <p className="text-gray-600">Coming soon...</p>
                    </div>
                );
        }
    };

    return (
        <div className="flex h-screen">
            {/* Sidebar */}
            <div className="sidebar w-64 text-white p-6">
                <div className="mb-8">
                    <h1 className="text-2xl font-bold flex items-center">
                        <i className="fab fa-facebook-f mr-3"></i>
                        FaceBot Multi
                    </h1>
                    <p className="text-sm opacity-75 mt-1">Facebook Automation Suite</p>
                </div>
                
                <nav className="space-y-2">
                    {[
                        { id: 'dashboard', icon: 'fas fa-tachometer-alt', label: 'Dashboard' },
                        { id: 'profiles', icon: 'fas fa-users', label: 'Profiles' },
                        { id: 'automation', icon: 'fas fa-robot', label: 'Automation' },
                        { id: 'proxies', icon: 'fas fa-network-wired', label: 'Proxies' },
                        { id: 'logs', icon: 'fas fa-list-alt', label: 'Logs' },
                        { id: 'settings', icon: 'fas fa-cog', label: 'Settings' }
                    ].map(item => (
                        <a
                            key={item.id}
                            href="#"
                            className={`sidebar-item flex items-center p-3 rounded-lg ${currentPage === item.id ? 'active' : ''}`}
                            onClick={(e) => {
                                e.preventDefault();
                                setCurrentPage(item.id);
                            }}
                        >
                            <i className={`${item.icon} mr-3`}></i>
                            {item.label}
                        </a>
                    ))}
                </nav>
                
                <div className="mt-auto pt-8">
                    <div className="bg-white bg-opacity-10 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                            <span className="text-sm">System Status</span>
                            <span className="status-indicator status-active"></span>
                        </div>
                        <div className="text-xs opacity-75">
                            <div>Profiles: <span>{profiles.length}</span></div>
                            <div>Active: <span>{campaigns.length}</span></div>
                        </div>
                    </div>
                </div>
            </div>
            
            {/* Main Content */}
            <div className="flex-1 flex flex-col">
                {/* Header */}
                <header className="bg-white shadow-sm border-b p-4">
                    <div className="flex items-center justify-between">
                        <div>
                            <h2 className="text-2xl font-semibold text-gray-800">
                                {currentPage.charAt(0).toUpperCase() + currentPage.slice(1)}
                            </h2>
                            <p className="text-gray-600">
                                {currentPage === 'dashboard' && 'Overview of your automation activities'}
                                {currentPage === 'profiles' && 'Manage your Facebook profiles'}
                                {currentPage === 'automation' && 'Create and manage automation campaigns'}
                                {currentPage === 'proxies' && 'Manage proxy servers'}
                                {currentPage === 'logs' && 'View automation activity logs'}
                                {currentPage === 'settings' && 'Configure application settings'}
                            </p>
                        </div>
                        <div className="flex items-center space-x-4">
                            <button 
                                className="p-2 text-gray-500 hover:text-gray-700 rounded-lg hover:bg-gray-100"
                                onClick={refreshCurrentPage}
                            >
                                <i className="fas fa-sync-alt"></i>
                            </button>
                            <div className="flex items-center space-x-2 text-sm text-gray-500">
                                <i className="fas fa-clock"></i>
                                <span id="current-time"></span>
                            </div>
                        </div>
                    </div>
                </header>
                
                {/* Page Content */}
                <main className="flex-1 p-6 overflow-auto">
                    <div className="fade-in">
                        {renderPageContent()}
                    </div>
                </main>
            </div>
            
            {/* Loading Overlay */}
            {loading && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg p-6 flex items-center space-x-4">
                        <div className="loading-spinner"></div>
                        <span>{loadingText}</span>
                    </div>
                </div>
            )}
        </div>
    );
}

// Initialize React app
const container = document.getElementById('app');
if (container) {
    const root = createRoot(container);
    root.render(<FaceBotApp />);
} else {
    console.error('App container not found');
}
