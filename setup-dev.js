#!/usr/bin/env node

const fs = require('fs').promises;
const path = require('path');

class DevSetup {
    constructor() {
        this.projectRoot = process.cwd();
    }

    async run() {
        console.log('🚀 Setting up FaceBot Multi for Development...\n');
        
        try {
            await this.checkNodeVersion();
            await this.createDirectories();
            await this.testCoreComponents();
            await this.createDevFiles();
            
            console.log('\n✅ Development setup completed!');
            console.log('\n📋 Next steps:');
            console.log('   1. Install dependencies: npm install');
            console.log('   2. Test core: npm run test-core');
            console.log('   3. Start development: npm run dev');
            console.log('\n💡 Tips:');
            console.log('   • Use npm run migrate to import from facebot.py');
            console.log('   • Use npm run clean to reset data');
            console.log('   • Check console for detailed logs in dev mode\n');
            
        } catch (error) {
            console.error('\n❌ Setup failed:', error.message);
            process.exit(1);
        }
    }

    async checkNodeVersion() {
        console.log('🔍 Checking Node.js version...');
        
        const nodeVersion = process.version;
        const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
        
        if (majorVersion < 16) {
            throw new Error(`Node.js 16 or higher is required. Current version: ${nodeVersion}`);
        }
        
        console.log(`   ✅ Node.js ${nodeVersion} is compatible`);
    }

    async createDirectories() {
        console.log('\n📁 Creating development directories...');
        
        const dirs = [
            'src/core',
            'src/database', 
            'src/renderer',
            'profiles',
            'data'
        ];
        
        for (const dir of dirs) {
            const fullPath = path.join(this.projectRoot, dir);
            try {
                await fs.mkdir(fullPath, { recursive: true });
                console.log(`   ✅ Created: ${dir}`);
            } catch (error) {
                if (error.code !== 'EEXIST') {
                    throw new Error(`Failed to create directory ${dir}: ${error.message}`);
                }
                console.log(`   ℹ️  Exists: ${dir}`);
            }
        }
    }

    async testCoreComponents() {
        console.log('\n🧪 Testing core components...');
        
        const requiredFiles = [
            'src/main.js',
            'src/preload.js',
            'src/core/FacebookBot.js',
            'src/core/ProfileManager.js',
            'src/core/AutomationController.js',
            'src/database/DatabaseManager.js',
            'src/renderer/index.html',
            'src/renderer/app.js'
        ];
        
        for (const file of requiredFiles) {
            const filePath = path.join(this.projectRoot, file);
            try {
                await fs.access(filePath);
                console.log(`   ✅ ${file}`);
            } catch (error) {
                console.log(`   ❌ Missing: ${file}`);
                throw new Error(`Required file missing: ${file}`);
            }
        }
    }

    async createDevFiles() {
        console.log('\n📝 Creating development files...');
        
        // Create .env file for development
        const envContent = `NODE_ENV=development
DEBUG=true
LOG_LEVEL=debug
HEADLESS=false`;
        
        try {
            await fs.writeFile('.env', envContent);
            console.log('   ✅ Created .env file');
        } catch (error) {
            console.log('   ⚠️  Failed to create .env file');
        }

        // Create development README
        const devReadme = `# FaceBot Multi - Development Mode

## Quick Start
\`\`\`bash
npm install
npm run test-core
npm run dev
\`\`\`

## Development Commands
- \`npm run dev\` - Start in development mode
- \`npm run test-core\` - Test core functionality
- \`npm run migrate\` - Import from facebot.py
- \`npm run clean\` - Clean data/profiles
- \`npm run reset\` - Clean and test

## Development Features
- Electron DevTools enabled
- Console logging enabled
- Hot reload (manual restart)
- Non-headless browser mode

## File Structure
- \`src/\` - Source code
- \`data/\` - SQLite database
- \`profiles/\` - Chrome userDataDir
- \`.env\` - Development environment variables

## Tips
- Check browser console for frontend logs
- Check terminal for backend logs
- Use Electron DevTools for debugging
- Test with single profile first
`;

        try {
            await fs.writeFile('DEV_README.md', devReadme);
            console.log('   ✅ Created DEV_README.md');
        } catch (error) {
            console.log('   ⚠️  Failed to create DEV_README.md');
        }

        // Create simple development launcher
        const devLauncher = `#!/usr/bin/env node

console.log('🚀 Starting FaceBot Multi Development Mode...');
console.log('');
console.log('📋 Development Features:');
console.log('   • Electron DevTools enabled');
console.log('   • Console logging enabled');
console.log('   • Non-headless browser mode');
console.log('   • Hot reload (manual restart)');
console.log('');
console.log('💡 Tips:');
console.log('   • Check browser console for frontend logs');
console.log('   • Check this terminal for backend logs');
console.log('   • Use Ctrl+Shift+I for DevTools');
console.log('   • Use Ctrl+R to reload the app');
console.log('');

// Set development environment
process.env.NODE_ENV = 'development';
process.env.DEBUG = 'true';

// Start the main application
require('./src/main.js');
`;

        try {
            await fs.writeFile('dev.js', devLauncher);
            console.log('   ✅ Created dev.js launcher');
        } catch (error) {
            console.log('   ⚠️  Failed to create dev.js launcher');
        }
    }
}

// Run setup if this file is executed directly
if (require.main === module) {
    const setup = new DevSetup();
    setup.run().catch(error => {
        console.error('Development setup failed:', error);
        process.exit(1);
    });
}

module.exports = DevSetup;
