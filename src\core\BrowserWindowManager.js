const { BrowserWindow } = require('electron');
const fs = require('fs');
const { spawn } = require('child_process');

class BrowserWindowManager {
    /**
     * @param {ProfileManager} profileManager
     * @param {DatabaseManager} dbManager
     */
    constructor(profileManager, dbManager) {
        if (!profileManager) throw new Error('ProfileManager is required.');
        if (!dbManager) throw new Error('DatabaseManager is required.');

        this.profileManager = profileManager;
        this.dbManager = dbManager;
        this.windows = new Map(); // Map<windowId, { process: ChildProcess, profileId: string }>
        this.activeProfiles = new Set(); // Set<profileId> - Tracks profiles currently in use by a window
    }

    /**
     * Launches a new Chrome browser window with profile
     * @param {object} options - Options for launching the window
     * @param {boolean} options.googleLoginMode - Whether to open in Google login mode
     * @param {string} options.profileId - Specific profile ID to use (optional)
     * @param {string} options.initialUrl - Initial URL to navigate to (optional)
     * @returns {Promise<{windowId: string, process: ChildProcess}|null>} Object containing ID and process, or null if failed.
     */
    async launchNewWindow(options = {}) {
        const googleLoginMode = options.googleLoginMode || false;

        // Log the number of active windows
        console.log(`Launching new window. Current active windows: ${this.windows.size}`);

        // --- Get Profile ---
        let nextProfile = null;

        if (options.profileId) {
            // Use specified profile if provided
            nextProfile = this.profileManager.getProfile(options.profileId);
            if (!nextProfile) {
                console.warn(`Specified profile ${options.profileId} not found.`);
                return null;
            }

            // Check if profile is already active
            if (this.activeProfiles.has(options.profileId)) {
                console.warn(`Specified profile ${options.profileId} is already active in another window.`);
                return null;
            }

            console.log(`Using specified profile for new window: ${nextProfile.id} (Name: ${nextProfile.name || 'N/A'})`);
        } else {
            // Get next available profile
            const allProfiles = this.profileManager.getAllProfiles();
            const availableProfiles = allProfiles
                .filter(p => !this.activeProfiles.has(p.id))
                .sort((a, b) => (a.lastUsed || 0) - (b.lastUsed || 0));

            if (availableProfiles.length === 0) {
                console.warn('Cannot launch new window: No available profiles found.');
                return null;
            }
            nextProfile = availableProfiles[0];
            console.log(`Selected profile for new window: ${nextProfile.id} (Name: ${nextProfile.name || 'N/A'})`);
        }

        // --- Prepare Chrome Launch ---
        try {
            console.log(`Launching Chrome for profile ${nextProfile.id} with userDataDir: ${nextProfile.userDataDir}`);

            // Check if userDataDir exists
            if (!fs.existsSync(nextProfile.userDataDir)) {
                console.warn(`userDataDir for profile ${nextProfile.id} (${nextProfile.userDataDir}) not found. Attempting to recreate.`);
                try {
                    fs.mkdirSync(nextProfile.userDataDir, { recursive: true });
                    console.log(`📁 Recreated missing userDataDir for profile ${nextProfile.id}: ${nextProfile.userDataDir}`);
                } catch (dirError) {
                    console.error(`❌ Failed to recreate missing userDataDir for profile ${nextProfile.id}. Cannot launch.`, dirError);
                    return null;
                }
            }

            // Prepare Chrome arguments
            const chromeArgs = [
                `--user-data-dir=${nextProfile.userDataDir}`,
                '--no-first-run',
                '--no-default-browser-check',
                '--disable-default-apps',
                '--disable-popup-blocking',
                '--disable-translate',
                '--disable-background-timer-throttling',
                '--disable-renderer-backgrounding',
                '--disable-backgrounding-occluded-windows',
                '--disable-ipc-flooding-protection',
                '--window-size=1200,800',
                '--window-position=100,100'
            ];

            // Add initial URL if provided
            if (options.initialUrl) {
                chromeArgs.push(options.initialUrl);
            } else {
                chromeArgs.push('https://facebook.com');
            }

            // Find Chrome executable
            const chromePaths = [
                'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
                'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe',
                process.env.CHROME_PATH,
                'google-chrome',
                'chrome'
            ].filter(Boolean);

            let chromePath = null;
            for (const path of chromePaths) {
                if (fs.existsSync(path)) {
                    chromePath = path;
                    break;
                }
            }

            if (!chromePath) {
                console.error('❌ Chrome executable not found. Please install Google Chrome.');
                return null;
            }

            console.log(`🚀 Launching Chrome: ${chromePath}`);
            console.log(`📁 Profile directory: ${nextProfile.userDataDir}`);

            // Launch Chrome process
            const chromeProcess = spawn(chromePath, chromeArgs, {
                detached: true,
                stdio: 'ignore'
            });

            // Generate window ID
            const windowId = `window-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

            // Set up process monitoring
            chromeProcess.on('exit', (code, signal) => {
                console.log(`Chrome process for window ${windowId} exited with code ${code}, signal ${signal}`);
                this.handleProcessExit(windowId);
            });

            chromeProcess.on('error', (error) => {
                console.error(`Chrome process error for window ${windowId}:`, error);
                this.handleProcessExit(windowId);
            });

            // Mark profile as active
            this.activeProfiles.add(nextProfile.id);

            // Store window data
            this.windows.set(windowId, {
                process: chromeProcess,
                profileId: nextProfile.id,
                createdAt: Date.now(),
                googleLoginMode: googleLoginMode
            });

            // Update profile last used
            this.profileManager.updateProfile(nextProfile.id, { lastUsed: Date.now() });

            console.log(`✅ Chrome window ${windowId} launched successfully for profile ${nextProfile.id}`);

            return {
                windowId: windowId,
                process: chromeProcess,
                profileId: nextProfile.id
            };

        } catch (error) {
            console.error(`❌ Failed to launch Chrome for profile ${nextProfile.id}:`, error);
            return null;
        }
    }

    /**
     * Handles process exit and cleanup
     * @param {string} windowId - ID of the window whose process exited
     */
    handleProcessExit(windowId) {
        const windowData = this.windows.get(windowId);
        if (!windowData) {
            return; // Already cleaned up
        }

        try {
            // Mark profile as inactive
            if (windowData.profileId) {
                this.activeProfiles.delete(windowData.profileId);
                console.log(`Profile ${windowData.profileId} marked as inactive due to process exit.`);

                // Update profile state in database
                this.profileManager.updateProfile(windowData.profileId, {
                    isActive: false,
                    lastClosed: Date.now()
                });
            }

            // Remove from windows map
            this.windows.delete(windowId);
            console.log(`✅ Window ${windowId} cleaned up after process exit.`);

        } catch (error) {
            console.error(`❌ Error during process exit cleanup for window ${windowId}:`, error);
        }
    }

    /**
     * Closes a specific window and cleans up resources.
     * @param {string} windowId - ID of the window to close
     * @returns {Promise<boolean>} True if successfully closed, false otherwise
     */
    async closeWindow(windowId) {
        const windowData = this.windows.get(windowId);
        if (!windowData) {
            console.warn(`Window ${windowId} not found for closing.`);
            return false;
        }

        try {
            // Kill Chrome process
            if (windowData.process && !windowData.process.killed) {
                windowData.process.kill('SIGTERM');
                console.log(`Chrome process killed for window ${windowId}`);
            }

            // Mark profile as inactive
            if (windowData.profileId) {
                this.activeProfiles.delete(windowData.profileId);
                console.log(`Profile ${windowData.profileId} marked as inactive.`);
            }

            // Remove from windows map
            this.windows.delete(windowId);

            console.log(`✅ Window ${windowId} closed successfully.`);
            return true;

        } catch (error) {
            console.error(`❌ Failed to close window ${windowId}:`, error);
            return false;
        }
    }

    /**
     * Closes all windows and cleans up resources.
     * @returns {Promise<void>}
     */
    async closeAllWindows() {
        console.log(`Closing all ${this.windows.size} windows...`);

        const closePromises = [];
        for (const [windowId] of this.windows) {
            closePromises.push(this.closeWindow(windowId));
        }

        await Promise.all(closePromises);
        console.log('✅ All windows closed.');
    }

    /**
     * Gets information about all active windows.
     * @returns {Array} Array of window information objects
     */
    getActiveWindows() {
        const windows = [];
        for (const [windowId, windowData] of this.windows) {
            windows.push({
                windowId: windowId,
                profileId: windowData.profileId,
                createdAt: windowData.createdAt,
                googleLoginMode: windowData.googleLoginMode || false
            });
        }
        return windows;
    }

    /**
     * Gets the number of active windows.
     * @returns {number} Number of active windows
     */
    getActiveWindowCount() {
        return this.windows.size;
    }

    /**
     * Gets the context for a specific window.
     * @param {string} windowId - ID of the window
     * @returns {playwright.BrowserContext|null} The browser context or null if not found
     */
    getWindowContext(windowId) {
        const windowData = this.windows.get(windowId);
        return windowData ? windowData.context : null;
    }

    /**
     * Force cleanup all stuck profiles and windows
     * @returns {Promise<void>}
     */
    async forceCleanupStuckProfiles() {
        console.log('🧹 Force cleaning up stuck profiles...');

        // Clear all active profiles
        const stuckProfiles = Array.from(this.activeProfiles);
        this.activeProfiles.clear();

        // Update all profiles to inactive in database
        for (const profileId of stuckProfiles) {
            try {
                this.profileManager.updateProfile(profileId, {
                    isActive: false,
                    lastClosed: Date.now()
                });
                console.log(`🧹 Cleaned up stuck profile: ${profileId}`);
            } catch (error) {
                console.error(`❌ Failed to cleanup profile ${profileId}:`, error);
            }
        }

        // Clear all windows
        this.windows.clear();

        console.log(`✅ Force cleanup completed. Cleaned ${stuckProfiles.length} stuck profiles.`);
    }

    /**
     * Check and cleanup dead processes
     * @returns {Promise<void>}
     */
    async cleanupDeadProcesses() {
        console.log('🔍 Checking for dead processes...');

        const deadWindows = [];

        for (const [windowId, windowData] of this.windows) {
            if (windowData.process && windowData.process.killed) {
                deadWindows.push(windowId);
            }
        }

        for (const windowId of deadWindows) {
            console.log(`🧹 Cleaning up dead process for window: ${windowId}`);
            this.handleProcessExit(windowId);
        }

        console.log(`✅ Dead process cleanup completed. Cleaned ${deadWindows.length} dead processes.`);
    }
}

module.exports = BrowserWindowManager;
