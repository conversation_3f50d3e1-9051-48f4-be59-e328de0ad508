@echo off
echo ========================================
echo    FaceBot Multi - Quick Start Script
echo ========================================
echo.

echo [1/4] Testing core functionality...
node test-core.js
if %errorlevel% neq 0 (
    echo ERROR: Core test failed!
    pause
    exit /b 1
)

echo.
echo [2/4] Installing missing dependencies...
npm install better-sqlite3 uuid playwright --no-optional
if %errorlevel% neq 0 (
    echo WARNING: Some dependencies may have failed to install
    echo This is normal on Windows without Visual Studio Build Tools
)

echo.
echo [3/4] Installing Playwright browsers...
npx playwright install chromium
if %errorlevel% neq 0 (
    echo WARNING: Playwright browser installation failed
    echo You may need to install manually later
)

echo.
echo [4/4] Starting FaceBot Multi...
echo.
echo ========================================
echo    Application is starting...
echo    Check the Electron window that opens
echo ========================================
echo.

npm start

pause
