<div tabindex="0" data-focusable="true" data-tti-phase="-1" data-action-id="32728" data-actual-height="38" data-mcomponent="MContainer" data-type="container" class="m bg-s11" style="height:38px; z-index:0; width:484px;"><div data-tti-phase="-1" data-mcomponent="ServerTextArea" data-type="text" class="m" style="margin-top:9px; height:21px; z-index:0; width:133px; margin-left:16px;"><div dir="auto" class="native-text">Write something...</div></div></div>


itu yang harus diklik, lalu

ini untuk toogle post anonymously:
<div data-tti-phase="-1" data-type="container" data-mcomponent="MContainer" data-actual-height="16" class="m nb" style="margin-top:-19px; height:16px; z-index:0; clip-path:inset(0 0 0 0 round 14px); --nbrad:14px; --nbr:1px; margin-left:519px; --nbc:#bcc0c4; --nbt:1px; --nbb:1px; width:36px; --nbl:1px;"><div data-tti-phase="-1" data-mcomponent="TextArea" data-type="text" class="m bg-s4" style="margin-top:1px; height:14px; z-index:0; width:34px; margin-left:1px;"></div></div>

ini untuk mulai mengetik postingan:
<div role="button" tabindex="0" aria-label="What's on your mind, create a post" data-focusable="true" data-tti-phase="-1" data-action-id="5" data-comp-id="1" data-type="text" data-mcomponent="ServerTextArea" class="m" style="margin-top:7px; height:168px; z-index:0; width:557px; margin-left:7px;" data-client-focused-component="true"><div dir="auto" class="native-text" style="color:#666666;">Write something</div></div>

ini untuk mengirim postingan:
<div role="button" tabindex="0" data-focusable="true" data-tti-phase="-1" data-action-id="32752" data-actual-height="44" data-mcomponent="MContainer" data-type="container" class="m" style="margin-top:-44px; height:44px; z-index:0; width:232px; margin-left:360px;"><div data-tti-phase="-1" data-mcomponent="ServerTextArea" data-type="text" class="m" style="margin-top:11px; height:21px; z-index:0; width:47px; margin-left:176px;"><div dir="auto" class="native-text" style="color:#1763cf;"><span class="f2">POST</span></div></div></div>

itu semua harus diklik berurutan
