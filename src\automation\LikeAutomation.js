const { chromium } = require('playwright');
const SmartSelectorManager = require('../core/SmartSelectorManager');

class LikeAutomation {
    constructor(dbManager) {
        this.dbManager = dbManager;
        this.smartSelector = new SmartSelectorManager(dbManager);
        this.browser = null;
        this.context = null;
        this.page = null;
        this.isRunning = false;
    }

    async initialize(profileId) {
        try {
            console.log(`🚀 Initializing Like automation for profile: ${profileId}`);

            // Get profile data
            const profile = await this.dbManager.getProfile(profileId);
            if (!profile) {
                throw new Error(`Profile ${profileId} not found`);
            }

            // Launch browser with profile
            const profilePath = `${process.env.PROFILES_DIR}/${profileId}`;

            this.browser = await chromium.launchPersistentContext(profilePath, {
                headless: false,
                viewport: { width: 1366, height: 768 },
                userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu'
                ]
            });

            this.page = await this.browser.newPage();

            // Navigate to Facebook
            await this.page.goto('https://www.facebook.com', { waitUntil: 'networkidle' });

            console.log(`✅ Like automation initialized for profile: ${profileId}`);
            return { success: true };

        } catch (error) {
            console.error('❌ Failed to initialize Like automation:', error);
            await this.cleanup();
            return { success: false, error: error.message };
        }
    }

    async run(options = {}) {
        if (this.isRunning) {
            return { success: false, error: 'Like automation is already running' };
        }

        this.isRunning = true;

        try {
            console.log('🚀 Starting Like automation...');

            const {
                maxLikes = 10,
                delayBetweenLikes = 30000, // 30 seconds
                targetPosts = 'feed' // 'feed', 'groups', 'pages'
            } = options;

            let likesCount = 0;
            const results = {
                totalLikes: 0,
                successfulLikes: 0,
                failedLikes: 0,
                errors: []
            };

            // Wait for page to load
            await this.page.waitForTimeout(3000);

            // Get like buttons using smart selector based on like.md inspection
            const likeSelectors = [
                // Primary selectors from like.md
                'div[aria-label="Like"][role="button"]',
                'div[aria-label="Like"][role="button"][tabindex="0"]',
                'div[role="button"][aria-label="Like"]',
                // Fallback selectors
                '[aria-label*="Like"]',
                '[data-testid="fb-ufi_likelink"]',
                'span[data-testid="react-button"]',
                // Additional patterns
                'div[role="button"]:has-text("Like")',
                'button[aria-label*="Like"]'
            ];

            for (let i = 0; i < maxLikes && this.isRunning; i++) {
                try {
                    console.log(`👍 Attempting like ${i + 1}/${maxLikes}`);

                    // Find like button using smart selector
                    let likeButton = null;
                    for (const selector of likeSelectors) {
                        try {
                            likeButton = await this.page.$(selector);
                            if (likeButton) {
                                // Check if it's actually a like button (not already liked)
                                const ariaLabel = await likeButton.getAttribute('aria-label');
                                if (ariaLabel && ariaLabel.toLowerCase().includes('like') && !ariaLabel.toLowerCase().includes('unlike')) {
                                    break;
                                }
                                likeButton = null;
                            }
                        } catch (error) {
                            continue;
                        }
                    }

                    if (likeButton) {
                        // Scroll to button
                        await likeButton.scrollIntoViewIfNeeded();
                        await this.page.waitForTimeout(1000);

                        // Click like button
                        await likeButton.click();

                        results.successfulLikes++;
                        console.log(`✅ Successfully liked post ${i + 1}`);

                        // Log the action
                        await this.dbManager.addLog({
                            profileId: 'current',
                            action: 'like_post',
                            target: 'facebook_post',
                            status: 'success',
                            message: `Successfully liked post ${i + 1}`
                        });

                    } else {
                        console.log(`⚠️ No like button found for post ${i + 1}`);
                        results.failedLikes++;

                        // Scroll down to find more posts
                        await this.page.evaluate(() => {
                            window.scrollBy(0, 500);
                        });
                        await this.page.waitForTimeout(2000);
                    }

                    // Wait between likes
                    if (i < maxLikes - 1) {
                        console.log(`⏳ Waiting ${delayBetweenLikes/1000} seconds before next like...`);
                        await this.page.waitForTimeout(delayBetweenLikes);
                    }

                } catch (error) {
                    console.error(`❌ Error liking post ${i + 1}:`, error);
                    results.failedLikes++;
                    results.errors.push(error.message);

                    await this.dbManager.addLog({
                        profileId: 'current',
                        action: 'like_post',
                        target: 'facebook_post',
                        status: 'error',
                        message: `Failed to like post ${i + 1}: ${error.message}`
                    });
                }
            }

            results.totalLikes = results.successfulLikes + results.failedLikes;

            console.log(`🎉 Like automation completed: ${results.successfulLikes}/${results.totalLikes} successful`);

            return {
                success: true,
                results
            };

        } catch (error) {
            console.error('❌ Like automation failed:', error);
            return { success: false, error: error.message };
        } finally {
            this.isRunning = false;
        }
    }

    async stop() {
        console.log('🛑 Stopping Like automation...');
        this.isRunning = false;
        await this.cleanup();
        return { success: true };
    }

    async cleanup() {
        try {
            if (this.page) {
                await this.page.close();
                this.page = null;
            }
            if (this.browser) {
                await this.browser.close();
                this.browser = null;
            }
            console.log('🧹 Like automation cleanup completed');
        } catch (error) {
            console.error('❌ Error during Like automation cleanup:', error);
        }
    }
}

module.exports = LikeAutomation;
