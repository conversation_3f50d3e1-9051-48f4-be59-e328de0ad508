const { chromium } = require('playwright');
const SmartSelectorManager = require('../core/SmartSelectorManager');

class ShareTimelineAutomation {
    constructor(dbManager) {
        this.dbManager = dbManager;
        this.smartSelector = new SmartSelectorManager(dbManager);
        this.browser = null;
        this.context = null;
        this.page = null;
        this.isRunning = false;
    }

    async initialize(profileId) {
        try {
            console.log(`🚀 Initializing Share Timeline automation for profile: ${profileId}`);
            
            // Get profile data
            const profile = await this.dbManager.getProfile(profileId);
            if (!profile) {
                throw new Error(`Profile ${profileId} not found`);
            }

            // Launch browser with profile
            const profilePath = `${process.env.PROFILES_DIR}/${profileId}`;
            
            this.browser = await chromium.launchPersistentContext(profilePath, {
                headless: false,
                viewport: { width: 1366, height: 768 },
                userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu'
                ]
            });

            this.page = await this.browser.newPage();
            
            // Navigate to Facebook
            await this.page.goto('https://www.facebook.com', { waitUntil: 'networkidle' });
            
            console.log(`✅ Share Timeline automation initialized for profile: ${profileId}`);
            return { success: true };
            
        } catch (error) {
            console.error('❌ Failed to initialize Share Timeline automation:', error);
            await this.cleanup();
            return { success: false, error: error.message };
        }
    }

    async run(options = {}) {
        if (this.isRunning) {
            return { success: false, error: 'Share Timeline automation is already running' };
        }

        this.isRunning = true;
        
        try {
            console.log('🚀 Starting Share Timeline automation...');
            
            const {
                maxShares = 3,
                delayBetweenShares = 120000, // 2 minutes
                shareText = "Check this out! 🔥",
                targetPosts = 'feed'
            } = options;

            const results = {
                totalShares: 0,
                successfulShares: 0,
                failedShares: 0,
                errors: []
            };

            // Wait for page to load
            await this.page.waitForTimeout(3000);

            // Share button selectors
            const shareSelectors = [
                '[aria-label*="Share"]',
                '[data-testid*="share"]',
                'div[role="button"][aria-label*="Share"]',
                'span[data-testid="share-button"]'
            ];

            for (let i = 0; i < maxShares && this.isRunning; i++) {
                try {
                    console.log(`📤 Attempting share ${i + 1}/${maxShares}`);
                    
                    // Find share button using smart selector
                    let shareButton = null;
                    for (const selector of shareSelectors) {
                        try {
                            shareButton = await this.page.$(selector);
                            if (shareButton) {
                                const isVisible = await shareButton.isVisible();
                                if (isVisible) break;
                                shareButton = null;
                            }
                        } catch (error) {
                            continue;
                        }
                    }

                    if (shareButton) {
                        // Scroll to share button
                        await shareButton.scrollIntoViewIfNeeded();
                        await this.page.waitForTimeout(1000);
                        
                        // Click share button
                        await shareButton.click();
                        await this.page.waitForTimeout(2000);
                        
                        // Look for "Share to your timeline" option
                        const shareToTimelineSelectors = [
                            'text="Share to your timeline"',
                            'text="Share now (Public)"',
                            'text="Share now"',
                            '[role="menuitem"]:has-text("timeline")',
                            '[role="menuitem"]:has-text("Share")'
                        ];
                        
                        let shareToTimelineButton = null;
                        for (const selector of shareToTimelineSelectors) {
                            try {
                                shareToTimelineButton = await this.page.$(selector);
                                if (shareToTimelineButton) {
                                    const isVisible = await shareToTimelineButton.isVisible();
                                    if (isVisible) break;
                                    shareToTimelineButton = null;
                                }
                            } catch (error) {
                                continue;
                            }
                        }
                        
                        if (shareToTimelineButton) {
                            await shareToTimelineButton.click();
                            await this.page.waitForTimeout(1000);
                            
                            // Look for text area to add share text
                            const textAreaSelectors = [
                                '[placeholder*="Say something"]',
                                '[placeholder*="Write something"]',
                                'div[contenteditable="true"][data-testid*="status"]',
                                'textarea[placeholder*="What\'s on your mind"]'
                            ];
                            
                            let textArea = null;
                            for (const selector of textAreaSelectors) {
                                try {
                                    textArea = await this.page.$(selector);
                                    if (textArea) {
                                        const isVisible = await textArea.isVisible();
                                        if (isVisible) break;
                                        textArea = null;
                                    }
                                } catch (error) {
                                    continue;
                                }
                            }
                            
                            if (textArea && shareText) {
                                await textArea.click();
                                await this.page.waitForTimeout(500);
                                await textArea.fill(shareText);
                                await this.page.waitForTimeout(1000);
                            }
                            
                            // Look for final share/post button
                            const postButtonSelectors = [
                                'text="Share"',
                                'text="Post"',
                                '[aria-label*="Share"]',
                                '[data-testid*="share-button"]'
                            ];
                            
                            let postButton = null;
                            for (const selector of postButtonSelectors) {
                                try {
                                    postButton = await this.page.$(selector);
                                    if (postButton) {
                                        const isVisible = await postButton.isVisible();
                                        if (isVisible) break;
                                        postButton = null;
                                    }
                                } catch (error) {
                                    continue;
                                }
                            }
                            
                            if (postButton) {
                                await postButton.click();
                                await this.page.waitForTimeout(2000);
                                
                                results.successfulShares++;
                                console.log(`✅ Successfully shared to timeline ${i + 1}`);
                                
                                // Log the action
                                await this.dbManager.addLog({
                                    profileId: 'current',
                                    action: 'share_timeline',
                                    target: 'facebook_timeline',
                                    status: 'success',
                                    message: `Successfully shared to timeline ${i + 1}`
                                });
                            } else {
                                throw new Error('Could not find post button');
                            }
                        } else {
                            throw new Error('Could not find share to timeline option');
                        }
                        
                    } else {
                        console.log(`⚠️ No share button found for post ${i + 1}`);
                        results.failedShares++;
                        
                        // Scroll down to find more posts
                        await this.page.evaluate(() => {
                            window.scrollBy(0, 500);
                        });
                        await this.page.waitForTimeout(2000);
                    }
                    
                    // Wait between shares
                    if (i < maxShares - 1) {
                        console.log(`⏳ Waiting ${delayBetweenShares/1000} seconds before next share...`);
                        await this.page.waitForTimeout(delayBetweenShares);
                    }
                    
                } catch (error) {
                    console.error(`❌ Error sharing to timeline ${i + 1}:`, error);
                    results.failedShares++;
                    results.errors.push(error.message);
                    
                    await this.dbManager.addLog({
                        profileId: 'current',
                        action: 'share_timeline',
                        target: 'facebook_timeline',
                        status: 'error',
                        message: `Failed to share to timeline ${i + 1}: ${error.message}`
                    });
                    
                    // Try to close any open modals
                    try {
                        await this.page.keyboard.press('Escape');
                        await this.page.waitForTimeout(1000);
                    } catch (e) {
                        // Ignore
                    }
                }
            }

            results.totalShares = results.successfulShares + results.failedShares;
            
            console.log(`🎉 Share Timeline automation completed: ${results.successfulShares}/${results.totalShares} successful`);
            
            return {
                success: true,
                results
            };
            
        } catch (error) {
            console.error('❌ Share Timeline automation failed:', error);
            return { success: false, error: error.message };
        } finally {
            this.isRunning = false;
        }
    }

    async stop() {
        console.log('🛑 Stopping Share Timeline automation...');
        this.isRunning = false;
        await this.cleanup();
        return { success: true };
    }

    async cleanup() {
        try {
            if (this.page) {
                await this.page.close();
                this.page = null;
            }
            if (this.browser) {
                await this.browser.close();
                this.browser = null;
            }
            console.log('🧹 Share Timeline automation cleanup completed');
        } catch (error) {
            console.error('❌ Error during Share Timeline automation cleanup:', error);
        }
    }
}

module.exports = ShareTimelineAutomation;
