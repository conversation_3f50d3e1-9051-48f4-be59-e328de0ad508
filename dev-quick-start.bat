@echo off
echo ========================================
echo    FaceBot Multi - Development Mode
echo ========================================
echo.

echo [1/3] Testing core functionality...
node test-core.js
if %errorlevel% neq 0 (
    echo ERROR: Core test failed!
    echo Please check the error messages above.
    pause
    exit /b 1
)

echo.
echo [2/3] Installing development dependencies...
npm install electron better-sqlite3 uuid playwright --no-optional
if %errorlevel% neq 0 (
    echo WARNING: Some dependencies failed to install
    echo This is normal on Windows without build tools
    echo The application should still work for basic testing
)

echo.
echo [3/3] Starting development mode...
echo.
echo ========================================
echo    Development Features Enabled:
echo    • Browser visible (non-headless)
echo    • Electron DevTools auto-open
echo    • Console logging enabled
echo    • Hot reload (Ctrl+R)
echo ========================================
echo.
echo 💡 Tips:
echo    • Use Ctrl+Shift+I for DevTools
echo    • Use Ctrl+R to reload app
echo    • Check terminal for backend logs
echo    • Check browser console for frontend logs
echo.

npm run dev

echo.
echo Development session ended.
pause
