# 🔧 FaceBot Multi - Development Guide

## 🚀 Quick Development Start

### **Prerequisites**
- Node.js 16+ 
- Git (optional)
- Facebook account untuk testing

### **Setup Development Environment**

1. **Install minimal dependencies:**
   ```bash
   npm install electron better-sqlite3 uuid playwright
   ```

2. **Test core functionality:**
   ```bash
   npm run test-core
   ```

3. **Start development mode:**
   ```bash
   npm run dev
   ```

## 🔧 Development Commands

| Command | Description |
|---------|-------------|
| `npm run dev` | Start in development mode |
| `npm run test-core` | Test core functionality without UI |
| `npm run migrate` | Import from existing facebot.py files |
| `npm run clean` | Clean data and mock profiles |
| `npm run reset` | Clean and run core test |
| `npm run setup-dev` | Setup development environment |

## 🎯 Development Features

### **Enabled in Development Mode**
- ✅ **Electron DevTools** - Automatic opening
- ✅ **Browser Visible** - Non-headless Playwright browsers
- ✅ **Console Logging** - Detailed logs in terminal
- ✅ **Hot Reload** - Manual restart with Ctrl+R
- ✅ **Debug Mode** - Additional debug information

### **Development Environment Variables**
```bash
NODE_ENV=development
DEBUG=true
LOG_LEVEL=debug
HEADLESS=false
```

## 📁 Project Structure for Development

```
facebot-multi/
├── dev-start.js              # Development launcher
├── test-core.js              # Core functionality test
├── migrate.js                # Migration from facebot.py
├── src/
│   ├── main.js               # Electron main process
│   ├── preload.js            # IPC bridge
│   ├── core/                 # Core automation logic
│   │   ├── FacebookBot.js    # Facebook automation
│   │   ├── ProfileManager.js # Profile management
│   │   └── AutomationController.js # Campaign management
│   ├── database/
│   │   └── DatabaseManager.js # SQLite operations
│   └── renderer/
│       ├── index.html        # UI template
│       └── app.js            # Frontend logic
├── data/                     # SQLite database (auto-created)
├── profiles/                 # Chrome userDataDir (auto-created)
└── package.json              # Dependencies
```

## 🧪 Testing & Debugging

### **1. Test Core Components**
```bash
# Test without UI - fastest way to verify logic
npm run test-core
```

### **2. Test with UI**
```bash
# Start full application
npm run dev
```

### **3. Test Migration**
```bash
# If you have facebot.py files
npm run migrate
npm run dev
```

### **4. Debug Browser Automation**
- Browser akan terlihat di development mode
- Bisa inspect element dan debug Playwright actions
- Check console untuk error messages

### **5. Debug Electron App**
- Use Ctrl+Shift+I untuk DevTools
- Check Console tab untuk frontend errors
- Check Network tab untuk IPC communication

## 🔍 Development Workflow

### **1. Initial Setup**
```bash
# Clone/download project
cd facebot-multi

# Install minimal dependencies
npm install electron better-sqlite3 uuid playwright

# Test core functionality
npm run test-core
```

### **2. Development Cycle**
```bash
# Start development
npm run dev

# Make changes to code
# Restart with Ctrl+R in Electron window
# Or restart terminal: npm run dev
```

### **3. Testing Changes**
```bash
# Test core logic changes
npm run test-core

# Test UI changes
npm run dev

# Test migration changes
npm run migrate
```

### **4. Reset Environment**
```bash
# Clean data and test
npm run reset

# Full clean and restart
npm run clean
npm run test-core
npm run dev
```

## 🐛 Common Development Issues

### **1. Dependencies Installation Failed**
```bash
# Try installing one by one
npm install electron
npm install better-sqlite3
npm install uuid
npm install playwright

# Or use --no-optional
npm install --no-optional
```

### **2. Database Issues**
```bash
# Reset database
npm run clean
npm run test-core
```

### **3. Playwright Browser Issues**
```bash
# Install browsers manually
npx playwright install chromium

# Or use system browser
# (modify FacebookBot.js to use system Chrome)
```

### **4. Electron Won't Start**
```bash
# Test core first
npm run test-core

# Check if electron is installed
npm list electron

# Reinstall electron
npm uninstall electron
npm install electron
```

### **5. Profile Test Failed**
- Check Facebook credentials
- Try manual login first
- Verify internet connection
- Check if Facebook account is locked

## 💡 Development Tips

### **1. Debugging Facebook Automation**
- Browser visible di development mode
- Bisa pause execution dengan debugger
- Check network requests di DevTools
- Test dengan single profile dulu

### **2. UI Development**
- Use Electron DevTools (Ctrl+Shift+I)
- Edit HTML/CSS langsung di DevTools
- Check Console untuk JavaScript errors
- Use React DevTools jika diperlukan

### **3. Database Development**
- Use SQLite browser untuk inspect database
- Check `data/facebot.db` file
- Use `npm run clean` untuk reset
- Test dengan sample data dulu

### **4. Performance Testing**
- Monitor memory usage di Task Manager
- Check browser processes
- Test dengan multiple profiles
- Monitor database size

## 🔧 Customization for Development

### **1. Modify Browser Settings**
Edit `src/core/FacebookBot.js`:
```javascript
const launchOptions = {
    headless: false,           // Always visible
    devtools: true,           // Open DevTools
    slowMo: 1000,            // Slow down actions
    userDataDir: this.profile.userDataDir
};
```

### **2. Add More Logging**
```javascript
console.log(`🔧 [${this.profile.name}] Action: ${action}`);
console.log(`📊 Status: ${status}`);
console.log(`⏱️  Delay: ${delay}ms`);
```

### **3. Test Mode**
Add test mode untuk skip actual Facebook actions:
```javascript
if (process.env.TEST_MODE === 'true') {
    console.log('🧪 Test mode: skipping actual Facebook action');
    return true;
}
```

## 📋 Development Checklist

### **Before Starting Development**
- [ ] Node.js 16+ installed
- [ ] Core test passes (`npm run test-core`)
- [ ] Basic dependencies installed
- [ ] Facebook account ready for testing

### **During Development**
- [ ] Test core logic changes with `npm run test-core`
- [ ] Test UI changes with `npm run dev`
- [ ] Check browser console for errors
- [ ] Check terminal for backend logs
- [ ] Test with single profile first

### **Before Committing Changes**
- [ ] Core test still passes
- [ ] Application starts without errors
- [ ] Basic functionality works
- [ ] No console errors
- [ ] Database operations work

## 🚀 Ready for Development!

Sekarang Anda siap untuk development:

1. **Start with:** `npm run test-core`
2. **Then:** `npm run dev`
3. **Debug with:** Browser DevTools + Electron DevTools
4. **Test with:** Single Facebook profile first

**Happy coding! 🎉**
