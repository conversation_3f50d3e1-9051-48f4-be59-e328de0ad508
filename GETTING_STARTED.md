# Getting Started with FaceBot Multi

## 🚀 Quick Start Guide

### Prerequisites
- Node.js 16 or higher
- Windows, macOS, or Linux
- At least 2GB free disk space
- Internet connection for initial setup

### Installation

1. **Clone or download the project**
   ```bash
   # If you have the project files, navigate to the directory
   cd facebot-multi
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Setup the application**
   ```bash
   npm run setup
   ```

4. **Install Playwright browsers** (if not done automatically)
   ```bash
   npx playwright install chromium
   ```

### First Run

1. **Start the application**
   ```bash
   npm start
   ```

2. **The application will open in a new window**
   - You'll see the dashboard with navigation sidebar
   - All stats will show 0 initially (this is normal)

### Setting Up Your First Profile

1. **Navigate to Profiles tab**
   - Click "Profiles" in the sidebar
   - Click "Add Profile" button

2. **Fill in profile information**
   - **Name**: Give your profile a descriptive name
   - **Email**: Your Facebook email/username
   - **Password**: Your Facebook password
   - **Proxy** (optional): Select a proxy if you have one configured

3. **Configure Facebook settings**
   - **Comments**: Add comment templates separated by `::`
   - **Shares**: Add share messages separated by `::`
   - **Decoy Links**: Add URLs to visit for human-like behavior
   - **Action Settings**: Enable/disable specific actions

4. **Test your profile**
   - Click the "Test" button to verify login works
   - Fix any issues before proceeding

### Migrating from Original FaceBot

If you have the original facebot.py files:

1. **Ensure files are in the project directory**
   - `accounts.txt` (format: email:password)
   - `comments.txt` (format: comment1 :: comment2 :: comment3)
   - `shares.txt` (format: share1 :: share2 :: share3)

2. **Run migration**
   ```bash
   npm run migrate
   ```

3. **Verify imported profiles**
   - Check the Profiles tab
   - Test a few profiles to ensure they work

### Creating Your First Campaign

1. **Navigate to Automation tab**
   - Click "Automation" in the sidebar
   - Click "Start Campaign" or "New Campaign"

2. **Configure campaign settings**
   - **Campaign Name**: Give it a descriptive name
   - **Actions**: Select what you want to do (comment, share, like, decoy)
   - **Target Post**: Enter the Facebook post URL or ID
   - **Profile Selection**: Choose which profiles to use
   - **Batch Settings**: Configure how many profiles to run simultaneously
   - **Timing**: Set delays and intervals

3. **Start the campaign**
   - Review your settings
   - Click "Start Campaign"
   - Monitor progress in real-time

### Understanding the Interface

#### Dashboard
- **Overview**: Quick stats and system status
- **Recent Activity**: Latest automation actions
- **Quick Actions**: Shortcuts to common tasks

#### Profiles
- **Profile List**: All your configured profiles
- **Status Indicators**: 
  - 🟢 Green: Available
  - 🔴 Red: Inactive
  - 🟡 Yellow: In cooldown
- **Actions**: Test, Edit, Delete profiles

#### Automation
- **Campaign Management**: Create and monitor campaigns
- **Real-time Progress**: Live updates on automation status
- **Batch Control**: Start, stop, pause campaigns

#### Proxies
- **Proxy Configuration**: Add and manage proxy servers
- **Health Monitoring**: Check proxy status
- **Rotation Settings**: Configure automatic proxy switching

#### Logs
- **Activity History**: Detailed logs of all actions
- **Filtering**: Search and filter by profile, action, status
- **Export**: Download logs for analysis

#### Settings
- **Global Configuration**: Application-wide settings
- **Automation Parameters**: Default delays and intervals
- **UI Preferences**: Theme and display options

### Best Practices

#### Profile Management
- **Use descriptive names** for easy identification
- **Test profiles regularly** to ensure they're working
- **Set appropriate cooldowns** to avoid detection
- **Rotate profiles** to distribute activity

#### Campaign Planning
- **Start small** with a few profiles to test
- **Use realistic delays** between actions
- **Monitor success rates** and adjust accordingly
- **Plan campaigns during peak hours** for better engagement

#### Security & Safety
- **Use proxies** when possible for additional anonymity
- **Don't run too many profiles simultaneously**
- **Vary your content** (comments, shares) to appear natural
- **Monitor for account restrictions** and adjust strategy

### Troubleshooting

#### Common Issues

**"Profile test failed"**
- Check email/password are correct
- Verify Facebook account isn't locked
- Try logging in manually first
- Check proxy settings if using one

**"No available profiles"**
- Check if profiles are active
- Wait for cooldown periods to expire
- Verify profiles aren't currently in use

**"Campaign won't start"**
- Ensure you have available profiles
- Check campaign configuration
- Verify target post URL is valid
- Look at logs for specific errors

**Application won't start**
- Check Node.js version (16+ required)
- Run `npm install` again
- Check for port conflicts
- Look at console for error messages

#### Getting Help

1. **Check the logs** in the Logs tab for detailed error messages
2. **Review the console** if running in development mode
3. **Verify your configuration** matches the examples
4. **Test with a single profile** first to isolate issues

### Advanced Configuration

#### Custom Delays
```javascript
// In profile Facebook settings
{
  "settings": {
    "delayComment": 4,      // seconds between comments
    "delayShare": 7,        // seconds between shares  
    "delayLogout": 3,       // seconds before logout
    "enableComments": true,
    "enableShares": true,
    "enableLikes": false,
    "enableDecoyLinks": true
  }
}
```

#### Proxy Configuration
- **HTTP/HTTPS proxies** supported
- **Authentication** with username/password
- **Health checking** and automatic failover
- **Rotation** for better anonymity

#### Database Backup
The application stores data in `data/facebot.db`. To backup:
```bash
# Copy the database file
cp data/facebot.db backup/facebot-backup-$(date +%Y%m%d).db
```

### Next Steps

1. **Set up multiple profiles** for better distribution
2. **Configure proxies** for enhanced security
3. **Create automation campaigns** with realistic settings
4. **Monitor and optimize** based on success rates
5. **Scale gradually** as you gain experience

### Support

For additional help:
- Check the main README.md for technical details
- Review the code comments for implementation details
- Test with the original facebot.py to compare behavior

---

**Remember**: Always comply with Facebook's Terms of Service and applicable laws. This tool is for educational and research purposes.
