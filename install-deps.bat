@echo off
echo Installing FaceBot Multi Dependencies...
echo.

echo [1/4] Installing Electron...
npm install electron --no-optional --force
if %errorlevel% neq 0 (
    echo WARNING: Electron installation failed
)

echo.
echo [2/4] Installing better-sqlite3...
npm install better-sqlite3 --no-optional --force
if %errorlevel% neq 0 (
    echo WARNING: better-sqlite3 installation failed
)

echo.
echo [3/4] Installing UUID...
npm install uuid --no-optional --force
if %errorlevel% neq 0 (
    echo WARNING: UUID installation failed
)

echo.
echo [4/4] Installing Playwright...
npm install playwright --no-optional --force
if %errorlevel% neq 0 (
    echo WARNING: Playwright installation failed
)

echo.
echo Installation completed!
echo You can now run: npm run dev
pause
