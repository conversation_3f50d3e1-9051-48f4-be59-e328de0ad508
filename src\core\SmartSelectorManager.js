const fs = require('fs');
const path = require('path');

class SmartSelectorManager {
    constructor(dbManager) {
        this.dbManager = dbManager;
        this.selectorCache = new Map();
        this.learnedSelectors = new Map();
        this.loadLearnedSelectors();

        // Primary selectors from HTML inspection
        this.primarySelectors = {
            like: [
                'div[aria-label="Like"][role="button"]',
                'div[aria-label*="Like"]',
                '[data-testid="like-button"]',
                'div[role="button"]:has-text("Like")'
            ],
            comment: [
                'div[aria-label="Write a comment…"][contenteditable="true"]',
                'div[aria-label*="comment"][contenteditable="true"]',
                '[data-testid="comment-input"]',
                'div[contenteditable="true"][aria-label*="Write"]'
            ],
            share: [
                'div[aria-label="Share"][role="button"]',
                'div[aria-label*="Share"]',
                '[data-testid="share-button"]',
                'div[role="button"]:has-text("Share")'
            ],
            post: [
                'div[aria-label="Post"][role="button"]',
                'div[aria-label*="Post"]',
                '[data-testid="post-button"]',
                'button:has-text("Post")'
            ]
        };
    }

    /**
     * Load learned selectors from database or file storage
     */
    loadLearnedSelectors() {
        try {
            const selectorsPath = path.join(__dirname, '..', '..', 'data', 'learned-selectors.json');
            if (fs.existsSync(selectorsPath)) {
                const data = JSON.parse(fs.readFileSync(selectorsPath, 'utf8'));
                this.learnedSelectors = new Map(Object.entries(data));
                console.log(`📚 Loaded ${this.learnedSelectors.size} learned selectors`);
            }
        } catch (error) {
            console.warn('Failed to load learned selectors:', error.message);
        }
    }

    /**
     * Save learned selectors to persistent storage
     */
    saveLearnedSelectors() {
        try {
            const selectorsPath = path.join(__dirname, '..', '..', 'data', 'learned-selectors.json');
            const dataDir = path.dirname(selectorsPath);

            if (!fs.existsSync(dataDir)) {
                fs.mkdirSync(dataDir, { recursive: true });
            }

            const data = Object.fromEntries(this.learnedSelectors);
            fs.writeFileSync(selectorsPath, JSON.stringify(data, null, 2));
            console.log(`💾 Saved ${this.learnedSelectors.size} learned selectors`);
        } catch (error) {
            console.error('Failed to save learned selectors:', error.message);
        }
    }

    /**
     * Find element using hybrid approach: primary selectors + smart discovery
     * @param {Page} page - Playwright page object
     * @param {string} action - Action type (like, comment, share, post)
     * @param {object} options - Additional options
     * @returns {Promise<{element: ElementHandle, selector: string}|null>}
     */
    async findElement(page, action, options = {}) {
        console.log(`🔍 Finding element for action: ${action}`);

        // Step 1: Try primary selectors first
        const primaryResult = await this.tryPrimarySelectors(page, action);
        if (primaryResult) {
            console.log(`✅ Found element using primary selector: ${primaryResult.selector}`);
            return primaryResult;
        }

        // Step 2: Try learned selectors
        const learnedResult = await this.tryLearnedSelectors(page, action);
        if (learnedResult) {
            console.log(`✅ Found element using learned selector: ${learnedResult.selector}`);
            return learnedResult;
        }

        // Step 3: Smart auto-discovery
        const discoveredResult = await this.smartAutoDiscovery(page, action, options);
        if (discoveredResult) {
            console.log(`✅ Found element using smart discovery: ${discoveredResult.selector}`);
            // Learn this selector for future use
            await this.learnSelector(action, discoveredResult.selector);
            return discoveredResult;
        }

        console.warn(`❌ Could not find element for action: ${action}`);
        return null;
    }

    /**
     * Try primary selectors from HTML inspection
     */
    async tryPrimarySelectors(page, action) {
        const selectors = this.primarySelectors[action] || [];

        for (const selector of selectors) {
            try {
                const element = await page.$(selector);
                if (element) {
                    const isVisible = await element.isVisible();
                    if (isVisible) {
                        return { element, selector };
                    }
                }
            } catch (error) {
                // Continue to next selector
                continue;
            }
        }

        return null;
    }

    /**
     * Try learned selectors from previous successful discoveries
     */
    async tryLearnedSelectors(page, action) {
        const learnedSelectorsForAction = this.learnedSelectors.get(action) || [];

        // Sort by success rate (most successful first)
        const sortedSelectors = learnedSelectorsForAction.sort((a, b) => b.successRate - a.successRate);

        for (const selectorData of sortedSelectors) {
            try {
                const element = await page.$(selectorData.selector);
                if (element) {
                    const isVisible = await element.isVisible();
                    if (isVisible) {
                        // Update success rate
                        selectorData.successCount++;
                        selectorData.successRate = selectorData.successCount / selectorData.totalAttempts;
                        return { element, selector: selectorData.selector };
                    }
                }
            } catch (error) {
                // Update failure rate
                selectorData.totalAttempts++;
                selectorData.successRate = selectorData.successCount / selectorData.totalAttempts;
                continue;
            }
        }

        return null;
    }

    /**
     * Smart auto-discovery system for finding elements
     */
    async smartAutoDiscovery(page, action, options = {}) {
        console.log(`🤖 Starting smart auto-discovery for action: ${action}`);

        try {
            const candidates = await page.evaluate((actionType) => {
                const elements = document.querySelectorAll('[role="button"], button, [tabindex="0"], div[aria-label], [data-testid]');

                return Array.from(elements).map(el => {
                    const rect = el.getBoundingClientRect();
                    const computedStyle = window.getComputedStyle(el);

                    return {
                        selector: this.generateSelector(el),
                        text: el.textContent?.trim() || '',
                        ariaLabel: el.getAttribute('aria-label') || '',
                        dataTestId: el.getAttribute('data-testid') || '',
                        className: el.className || '',
                        tagName: el.tagName.toLowerCase(),
                        position: {
                            x: rect.x,
                            y: rect.y,
                            width: rect.width,
                            height: rect.height
                        },
                        isVisible: el.offsetParent !== null && computedStyle.visibility !== 'hidden' && computedStyle.display !== 'none',
                        hasClickHandler: el.onclick !== null || el.addEventListener !== undefined
                    };
                }).filter(el => el.isVisible && (el.position.width > 0 && el.position.height > 0));
            }, action);

            // Score and filter candidates based on action type
            const scoredCandidates = this.scoreCandidates(candidates, action);

            // Try candidates in order of score
            for (const candidate of scoredCandidates) {
                try {
                    const element = await page.$(candidate.selector);
                    if (element) {
                        const isVisible = await element.isVisible();
                        if (isVisible) {
                            // Verify this is the right element by testing interaction
                            const isCorrect = await this.verifyElement(page, element, action);
                            if (isCorrect) {
                                return { element, selector: candidate.selector };
                            }
                        }
                    }
                } catch (error) {
                    continue;
                }
            }

        } catch (error) {
            console.error('Smart auto-discovery failed:', error.message);
        }

        return null;
    }

    /**
     * Score candidates based on relevance to action
     */
    scoreCandidates(candidates, action) {
        const actionKeywords = {
            like: ['like', 'react', 'love', 'heart'],
            comment: ['comment', 'reply', 'write', 'add'],
            share: ['share', 'repost', 'forward'],
            post: ['post', 'publish', 'submit', 'send']
        };

        const keywords = actionKeywords[action] || [];

        return candidates.map(candidate => {
            let score = 0;

            // Text content matching
            const text = candidate.text.toLowerCase();
            const ariaLabel = candidate.ariaLabel.toLowerCase();

            keywords.forEach(keyword => {
                if (text.includes(keyword)) score += 10;
                if (ariaLabel.includes(keyword)) score += 15;
                if (candidate.dataTestId.toLowerCase().includes(keyword)) score += 20;
            });

            // Position scoring (prefer elements in typical locations)
            if (action === 'like' && candidate.position.y > 100) score += 5;
            if (action === 'comment' && candidate.position.y > 200) score += 5;
            if (action === 'share' && candidate.position.y > 100) score += 5;

            // Size scoring (prefer reasonably sized elements)
            const area = candidate.position.width * candidate.position.height;
            if (area > 100 && area < 10000) score += 3;

            // Role and tag scoring
            if (candidate.tagName === 'button') score += 5;
            if (candidate.ariaLabel) score += 3;
            if (candidate.hasClickHandler) score += 2;

            return { ...candidate, score };
        }).sort((a, b) => b.score - a.score);
    }

    /**
     * Verify that an element is correct for the action
     */
    async verifyElement(page, element, action) {
        try {
            // For now, we'll do a simple verification
            // In a more sophisticated implementation, you could:
            // 1. Check if clicking changes the element state
            // 2. Look for specific visual feedback
            // 3. Check for network requests after interaction

            const ariaLabel = await element.getAttribute('aria-label');
            const text = await element.textContent();

            const actionKeywords = {
                like: ['like', 'react'],
                comment: ['comment', 'write'],
                share: ['share'],
                post: ['post', 'publish']
            };

            const keywords = actionKeywords[action] || [];
            const content = `${ariaLabel} ${text}`.toLowerCase();

            return keywords.some(keyword => content.includes(keyword));
        } catch (error) {
            return false;
        }
    }

    /**
     * Learn a successful selector for future use
     */
    async learnSelector(action, selector) {
        if (!this.learnedSelectors.has(action)) {
            this.learnedSelectors.set(action, []);
        }

        const actionSelectors = this.learnedSelectors.get(action);
        const existing = actionSelectors.find(s => s.selector === selector);

        if (existing) {
            existing.successCount++;
            existing.totalAttempts++;
            existing.successRate = existing.successCount / existing.totalAttempts;
            existing.lastUsed = Date.now();
        } else {
            actionSelectors.push({
                selector: selector,
                successCount: 1,
                totalAttempts: 1,
                successRate: 1.0,
                learnedAt: Date.now(),
                lastUsed: Date.now()
            });
        }

        // Save to persistent storage
        this.saveLearnedSelectors();

        console.log(`📚 Learned selector for ${action}: ${selector}`);
    }

    /**
     * Record a failure for learning purposes
     * @param {string} action - Action type
     * @param {string} errorMessage - Error message
     */
    async recordFailure(action, errorMessage) {
        console.log(`❌ Recording failure for ${action}: ${errorMessage}`);

        // You could implement more sophisticated failure tracking here
        // For now, we'll just log it
        if (this.dbManager) {
            try {
                await this.dbManager.addLog({
                    profileId: 'system',
                    action: `selector_failure_${action}`,
                    target: '',
                    status: 'error',
                    message: `Smart selector failure: ${errorMessage}`
                });
            } catch (error) {
                console.warn('Failed to log selector failure:', error.message);
            }
        }
    }

    /**
     * Get statistics about learned selectors
     */
    getStats() {
        const stats = {};
        for (const [action, selectors] of this.learnedSelectors) {
            stats[action] = {
                totalSelectors: selectors.length,
                avgSuccessRate: selectors.reduce((sum, s) => sum + s.successRate, 0) / selectors.length,
                mostSuccessful: selectors.reduce((best, current) =>
                    current.successRate > best.successRate ? current : best, selectors[0])
            };
        }
        return stats;
    }
}

module.exports = SmartSelectorManager;
