const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');
const { promisify } = require('util');

class ImportExportManager {
    constructor(profileManager, dbManager) {
        this.profileManager = profileManager;
        this.dbManager = dbManager;
        this.tempDir = path.join(__dirname, '..', '..', 'temp');
        this.ensureTempDir();
    }

    ensureTempDir() {
        if (!fs.existsSync(this.tempDir)) {
            fs.mkdirSync(this.tempDir, { recursive: true });
            console.log(`📁 Created temp directory: ${this.tempDir}`);
        }
    }

    /**
     * Export profiles to RAR archive
     * @param {Array} profileIds - Array of profile IDs to export
     * @param {string} exportPath - Path where to save the RAR file
     * @param {object} options - Export options
     * @returns {Promise<object>} Export result
     */
    async exportProfilesToRAR(profileIds, exportPath, options = {}) {
        try {
            console.log(`📦 Starting export of ${profileIds.length} profiles to RAR...`);

            // Create temporary export directory
            const exportTempDir = path.join(this.tempDir, `export-${Date.now()}`);
            fs.mkdirSync(exportTempDir, { recursive: true });

            const exportedProfiles = [];
            const errors = [];

            // Copy profile directories and metadata
            for (const profileId of profileIds) {
                try {
                    const profile = this.profileManager.getProfile(profileId);
                    if (!profile) {
                        errors.push(`Profile ${profileId} not found`);
                        continue;
                    }

                    // Create profile export directory
                    const profileExportDir = path.join(exportTempDir, profileId);
                    fs.mkdirSync(profileExportDir, { recursive: true });

                    // Copy profile data directory if exists
                    if (profile.userDataDir && fs.existsSync(profile.userDataDir)) {
                        await this.copyDirectory(profile.userDataDir, path.join(profileExportDir, 'userdata'));
                    }

                    // Save profile metadata
                    const metadata = {
                        ...profile,
                        exportedAt: Date.now(),
                        exportVersion: '1.0'
                    };

                    fs.writeFileSync(
                        path.join(profileExportDir, 'metadata.json'),
                        JSON.stringify(metadata, null, 2)
                    );

                    exportedProfiles.push(profileId);
                    console.log(`✅ Exported profile: ${profile.name}`);

                } catch (error) {
                    console.error(`❌ Failed to export profile ${profileId}:`, error.message);
                    errors.push(`Profile ${profileId}: ${error.message}`);
                }
            }

            // Create export manifest
            const manifest = {
                exportedAt: Date.now(),
                exportVersion: '1.0',
                profileCount: exportedProfiles.length,
                profiles: exportedProfiles,
                errors: errors,
                options: options
            };

            fs.writeFileSync(
                path.join(exportTempDir, 'manifest.json'),
                JSON.stringify(manifest, null, 2)
            );

            // Debug: List files before creating RAR
            try {
                const exportFiles = fs.readdirSync(exportTempDir, { recursive: true });
                console.log(`📦 Files to be archived: ${exportFiles.join(', ')}`);
            } catch (error) {
                console.log(`❌ Could not list export files: ${error.message}`);
            }

            // Create RAR archive
            console.log(`📦 Creating RAR archive: ${exportPath}`);
            const rarResult = await this.createRARArchive(exportTempDir, exportPath);

            // Cleanup temp directory
            await this.removeDirectory(exportTempDir);

            if (rarResult.success) {
                console.log(`✅ Export completed: ${exportPath}`);
                return {
                    success: true,
                    exportPath: exportPath,
                    profileCount: exportedProfiles.length,
                    errors: errors,
                    message: `Successfully exported ${exportedProfiles.length} profiles`
                };
            } else {
                throw new Error(`RAR creation failed: ${rarResult.error}`);
            }

        } catch (error) {
            console.error('❌ Export failed:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Import profiles from RAR archive
     * @param {string} rarPath - Path to the RAR file
     * @param {object} options - Import options
     * @returns {Promise<object>} Import result
     */
    async importProfilesFromRAR(rarPath, options = {}) {
        try {
            console.log(`📥 Starting import from RAR: ${rarPath}`);

            if (!fs.existsSync(rarPath)) {
                throw new Error('RAR file not found');
            }

            // Create temporary import directory
            const importTempDir = path.join(this.tempDir, `import-${Date.now()}`);
            fs.mkdirSync(importTempDir, { recursive: true });

            // Extract RAR archive
            console.log(`📦 Extracting RAR to: ${importTempDir}`);
            const extractResult = await this.extractRARArchive(rarPath, importTempDir);
            if (!extractResult.success) {
                throw new Error(`RAR extraction failed: ${extractResult.error}`);
            }
            console.log(`✅ RAR extraction completed`);

            // Debug: List extracted files
            try {
                const extractedFiles = fs.readdirSync(importTempDir);
                console.log(`📁 Extracted files: ${extractedFiles.join(', ')}`);
            } catch (error) {
                console.log(`❌ Could not list extracted files: ${error.message}`);
            }

            // Read manifest - handle nested structure
            let manifestPath = path.join(importTempDir, 'manifest.json');
            let actualImportDir = importTempDir;

            console.log(`🔍 Looking for manifest at: ${manifestPath}`);
            if (!fs.existsSync(manifestPath)) {
                // Try to find manifest in subdirectories
                try {
                    const files = fs.readdirSync(importTempDir, { recursive: true });
                    console.log(`📋 All extracted files: ${files.join(', ')}`);

                    // Look for manifest.json in subdirectories
                    const manifestFiles = files.filter(file => file.includes('manifest.json'));
                    if (manifestFiles.length > 0) {
                        console.log(`🔍 Found manifest files: ${manifestFiles.join(', ')}`);

                        // Use the first manifest found and adjust import directory
                        const manifestFile = manifestFiles[0];
                        manifestPath = path.join(importTempDir, manifestFile);
                        actualImportDir = path.dirname(manifestPath);
                        console.log(`📁 Using manifest: ${manifestPath}`);
                        console.log(`📁 Actual import directory: ${actualImportDir}`);
                    } else {
                        throw new Error('Invalid export file: manifest.json not found');
                    }
                } catch (error) {
                    console.log(`❌ Could not search for manifest: ${error.message}`);
                    throw new Error('Invalid export file: manifest.json not found');
                }
            }

            const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
            console.log(`📋 Import manifest: ${manifest.profileCount} profiles`);

            const importedProfiles = [];
            const errors = [];
            const skipped = [];

            // Import each profile
            for (const profileId of manifest.profiles) {
                try {
                    const profileDir = path.join(actualImportDir, profileId);
                    const metadataPath = path.join(profileDir, 'metadata.json');

                    if (!fs.existsSync(metadataPath)) {
                        errors.push(`Profile ${profileId}: metadata.json not found`);
                        continue;
                    }

                    const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));

                    // Check if profile already exists
                    const existingProfile = this.profileManager.getProfile(profileId);
                    if (existingProfile && !options.overwrite) {
                        skipped.push(`Profile ${metadata.name} already exists`);
                        continue;
                    }

                    // Generate new profile ID if needed
                    const newProfileId = options.generateNewIds ?
                        await this.generateUniqueProfileId() : profileId;

                    // Create new userDataDir in profiles directory
                    const profilesDir = path.join(process.cwd(), 'profiles');
                    const newUserDataDir = path.join(profilesDir, newProfileId);

                    // Copy userdata if exists
                    const userdataSource = path.join(profileDir, 'userdata');
                    if (fs.existsSync(userdataSource)) {
                        await this.copyDirectory(userdataSource, newUserDataDir);
                    } else {
                        // Create empty userdata directory
                        fs.mkdirSync(newUserDataDir, { recursive: true });
                    }

                    // Create profile in database
                    const newProfile = {
                        ...metadata,
                        id: newProfileId,
                        userDataDir: newUserDataDir,
                        importedAt: Date.now(),
                        importedFrom: rarPath
                    };

                    // Remove export-specific fields
                    delete newProfile.exportedAt;
                    delete newProfile.exportVersion;

                    const savedProfile = this.profileManager.saveProfile(newProfile);
                    if (savedProfile) {
                        importedProfiles.push(newProfile.name);
                        console.log(`✅ Imported profile: ${newProfile.name}`);
                    } else {
                        errors.push(`Failed to save profile: ${newProfile.name}`);
                    }

                } catch (error) {
                    console.error(`❌ Failed to import profile ${profileId}:`, error.message);
                    errors.push(`Profile ${profileId}: ${error.message}`);
                }
            }

            // Cleanup temp directory
            await this.removeDirectory(importTempDir);

            console.log(`✅ Import completed: ${importedProfiles.length} profiles imported`);
            return {
                success: true,
                importedCount: importedProfiles.length,
                importedProfiles: importedProfiles,
                skippedCount: skipped.length,
                skipped: skipped,
                errorCount: errors.length,
                errors: errors,
                message: `Successfully imported ${importedProfiles.length} profiles`
            };

        } catch (error) {
            console.error('❌ Import failed:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Create RAR archive using WinRAR or 7-Zip
     * @param {string} sourceDir - Directory to archive
     * @param {string} rarPath - Output RAR file path
     * @returns {Promise<object>} Result
     */
    async createRARArchive(sourceDir, rarPath) {
        return new Promise((resolve) => {
            // Try WinRAR first
            const winrarPath = 'C:\\Program Files\\WinRAR\\WinRAR.exe';

            if (fs.existsSync(winrarPath)) {
                const args = ['a', '-r', rarPath, `${sourceDir}\\*`];
                const winrar = spawn(winrarPath, args);

                winrar.on('close', (code) => {
                    if (code === 0) {
                        resolve({ success: true });
                    } else {
                        resolve({ success: false, error: `WinRAR exit code: ${code}` });
                    }
                });

                winrar.on('error', (error) => {
                    // Try 7-Zip as fallback
                    this.create7ZipArchive(sourceDir, rarPath).then(resolve);
                });
            } else {
                // Try 7-Zip
                this.create7ZipArchive(sourceDir, rarPath).then(resolve);
            }
        });
    }

    /**
     * Create archive using 7-Zip
     * @param {string} sourceDir - Directory to archive
     * @param {string} archivePath - Output archive file path
     * @returns {Promise<object>} Result
     */
    async create7ZipArchive(sourceDir, archivePath) {
        return new Promise((resolve) => {
            const sevenZipPath = 'C:\\Program Files\\7-Zip\\7z.exe';

            if (fs.existsSync(sevenZipPath)) {
                const args = ['a', '-t7z', archivePath, `${sourceDir}\\*`];
                const sevenZip = spawn(sevenZipPath, args);

                sevenZip.on('close', (code) => {
                    if (code === 0) {
                        resolve({ success: true });
                    } else {
                        resolve({ success: false, error: `7-Zip exit code: ${code}` });
                    }
                });

                sevenZip.on('error', (error) => {
                    resolve({ success: false, error: `7-Zip not available: ${error.message}` });
                });
            } else {
                resolve({ success: false, error: 'Neither WinRAR nor 7-Zip found' });
            }
        });
    }

    /**
     * Extract RAR archive
     * @param {string} rarPath - RAR file path
     * @param {string} extractDir - Directory to extract to
     * @returns {Promise<object>} Result
     */
    async extractRARArchive(rarPath, extractDir) {
        return new Promise((resolve) => {
            // Try WinRAR first
            const winrarPath = 'C:\\Program Files\\WinRAR\\WinRAR.exe';

            if (fs.existsSync(winrarPath)) {
                const args = ['x', '-y', rarPath, extractDir + '\\'];
                const winrar = spawn(winrarPath, args);

                winrar.on('close', (code) => {
                    if (code === 0) {
                        resolve({ success: true });
                    } else {
                        resolve({ success: false, error: `WinRAR exit code: ${code}` });
                    }
                });

                winrar.on('error', (error) => {
                    // Try 7-Zip as fallback
                    this.extract7ZipArchive(rarPath, extractDir).then(resolve);
                });
            } else {
                // Try 7-Zip
                this.extract7ZipArchive(rarPath, extractDir).then(resolve);
            }
        });
    }

    /**
     * Extract archive using 7-Zip
     * @param {string} archivePath - Archive file path
     * @param {string} extractDir - Directory to extract to
     * @returns {Promise<object>} Result
     */
    async extract7ZipArchive(archivePath, extractDir) {
        return new Promise((resolve) => {
            const sevenZipPath = 'C:\\Program Files\\7-Zip\\7z.exe';

            if (fs.existsSync(sevenZipPath)) {
                const args = ['x', archivePath, `-o${extractDir}`];
                const sevenZip = spawn(sevenZipPath, args);

                sevenZip.on('close', (code) => {
                    if (code === 0) {
                        resolve({ success: true });
                    } else {
                        resolve({ success: false, error: `7-Zip exit code: ${code}` });
                    }
                });

                sevenZip.on('error', (error) => {
                    resolve({ success: false, error: `7-Zip not available: ${error.message}` });
                });
            } else {
                resolve({ success: false, error: 'Neither WinRAR nor 7-Zip found' });
            }
        });
    }

    /**
     * Copy directory recursively
     * @param {string} src - Source directory
     * @param {string} dest - Destination directory
     */
    async copyDirectory(src, dest) {
        if (!fs.existsSync(dest)) {
            fs.mkdirSync(dest, { recursive: true });
        }

        const entries = fs.readdirSync(src, { withFileTypes: true });

        for (const entry of entries) {
            const srcPath = path.join(src, entry.name);
            const destPath = path.join(dest, entry.name);

            if (entry.isDirectory()) {
                await this.copyDirectory(srcPath, destPath);
            } else {
                fs.copyFileSync(srcPath, destPath);
            }
        }
    }

    /**
     * Remove directory recursively
     * @param {string} dirPath - Directory path to remove
     */
    async removeDirectory(dirPath) {
        if (fs.existsSync(dirPath)) {
            fs.rmSync(dirPath, { recursive: true, force: true });
        }
    }

    /**
     * Generate unique profile ID
     * @returns {Promise<string>} Unique profile ID
     */
    async generateUniqueProfileId() {
        const { v4: uuidv4 } = require('uuid');
        let newId = uuidv4();

        // Ensure uniqueness
        while (this.profileManager.getProfile(newId)) {
            newId = uuidv4();
        }

        return newId;
    }
}

module.exports = ImportExportManager;
