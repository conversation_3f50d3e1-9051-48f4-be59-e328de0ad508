# FaceBot Multi - Facebook Automation Multi-Profile Application

A modern, multi-profile Facebook automation application built with Electron, React, and Playwright. This application enhances the original working facebot.py with advanced profile management, real-time monitoring, and sophisticated automation capabilities.

## 🚀 Features

### Core Automation (Ported from facebot.py)
- ✅ **Facebook Login** - Mobile Facebook login for reliability
- ✅ **Comment System** - Automated commenting using `m.facebook.com/{postId}`
- ✅ **Share System** - Post sharing using `facebook.com/sharer/sharer.php?u=`
- ✅ **Like System** - Post liking with multiple selector fallbacks
- ✅ **Decoy Links** - Visit random links to simulate human behavior
- ✅ **Smart Delays** - Configurable delays between actions

### Advanced Multi-Profile Management
- 🔄 **Profile Rotation** - Intelligent profile selection and rotation
- 🕒 **Cooldown System** - Configurable cooldown periods between profile usage
- 📁 **Chrome Profiles** - Isolated Chrome userDataDir for each profile
- 🔍 **Profile Testing** - Test profile login before automation
- 📊 **Profile Statistics** - Track success rates and activity per profile

### Modern UI & Monitoring
- 🖥️ **Electron Interface** - Modern desktop application
- 📱 **Responsive Design** - Clean, intuitive interface with Tailwind CSS
- 📈 **Real-time Monitoring** - Live progress tracking and status updates
- 📋 **Activity Logs** - Comprehensive logging with filtering
- ⚙️ **Settings Management** - Configurable automation parameters

### Proxy & Security
- 🌐 **Proxy Support** - HTTP/HTTPS proxy rotation
- 🔒 **Anti-Detection** - Random delays, human-like scrolling
- 🎭 **User-Agent Rotation** - Multiple browser fingerprints
- 🛡️ **Error Handling** - Graceful failure recovery

### Campaign Management
- 📋 **Batch Processing** - Process profiles in configurable batches
- ⏱️ **Scheduling** - Automated campaign execution with intervals
- 🎯 **Action Selection** - Choose specific actions per campaign
- 📊 **Progress Tracking** - Real-time campaign progress monitoring

## 🏗️ Architecture

```
facebot-multi/
├── src/
│   ├── main.js              # Electron main process
│   ├── preload.js           # IPC bridge
│   ├── core/                # Core automation logic
│   │   ├── FacebookBot.js   # Main bot class (ported from facebot.py)
│   │   ├── ProfileManager.js # Profile management
│   │   └── AutomationController.js # Campaign management
│   ├── database/            # SQLite database
│   │   └── DatabaseManager.js
│   └── renderer/            # UI components
│       ├── index.html
│       └── app.js
├── profiles/                # Chrome profile storage
├── data/                    # SQLite database
└── package.json
```

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd facebot-multi
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Install Playwright browsers**
   ```bash
   npx playwright install chromium
   ```

## 🚀 Usage

### Development Mode
```bash
npm run dev
```

### Production Build
```bash
npm run build
```

### Start Application
```bash
npm start
```

## 📋 Migration from Original FaceBot

The application includes migration tools to import your existing facebot.py configuration:

1. **Import Accounts**: Import from `accounts.txt` (format: `email:password`)
2. **Import Comments**: Import from `comments.txt` (format: `comment1 :: comment2 :: comment3`)
3. **Import Shares**: Import from `shares.txt` (format: `share1 :: share2 :: share3`)

## ⚙️ Configuration

### Profile Settings
- **Email/Password**: Facebook login credentials
- **Proxy**: Optional proxy server assignment
- **Cooldown**: Time between profile usage
- **Actions**: Enable/disable specific automation actions

### Automation Settings
- **Batch Size**: Number of profiles to process simultaneously
- **Batch Interval**: Time between batches
- **Action Delays**: Delays between different actions
- **Decoy Settings**: Decoy link visit configuration

### Campaign Configuration
- **Actions**: Select comment, share, like, decoy actions
- **Post URL/ID**: Target post for automation
- **Profile Selection**: Choose specific profiles or use all available
- **Scheduling**: Set execution intervals and timing

## 🔧 Database Schema

### Profiles Table
```sql
CREATE TABLE profiles (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    email TEXT,
    password TEXT,
    userDataDir TEXT,
    proxyId TEXT,
    isActive BOOLEAN DEFAULT 1,
    lastUsed INTEGER,
    cooldownExpiresAt INTEGER,
    createdAt INTEGER,
    facebook TEXT -- JSON: {postLink, comments, decoyLinks, settings}
);
```

### Automation Logs Table
```sql
CREATE TABLE automation_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    profileId TEXT,
    action TEXT, -- 'like', 'comment', 'share', 'decoy'
    target TEXT, -- URL or content
    status TEXT, -- 'success', 'failed', 'skipped'
    message TEXT,
    timestamp INTEGER,
    campaignId TEXT
);
```

## 🛡️ Anti-Detection Features

- **Random Delays**: Configurable delays between actions
- **Human-like Scrolling**: Simulate natural browsing behavior
- **Profile Rotation**: Distribute actions across multiple profiles
- **Proxy Rotation**: Use different IP addresses
- **User-Agent Variation**: Multiple browser fingerprints
- **Mobile Facebook**: Use mobile interface for better reliability

## 📊 Monitoring & Analytics

- **Real-time Dashboard**: Live statistics and status
- **Profile Performance**: Success rates per profile
- **Campaign Progress**: Batch processing status
- **Activity Logs**: Detailed action history
- **Error Tracking**: Failed action analysis

## 🔒 Security Considerations

- **Local Storage**: All data stored locally in SQLite
- **Encrypted Profiles**: Chrome profiles with isolated sessions
- **Proxy Support**: Route traffic through proxy servers
- **Error Handling**: Graceful failure without data loss

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## ⚠️ Disclaimer

This tool is for educational and research purposes only. Users are responsible for complying with Facebook's Terms of Service and applicable laws. The developers are not responsible for any misuse of this software.

## 🆘 Support

For support and questions:
1. Check the documentation
2. Review existing issues
3. Create a new issue with detailed information

## 🔄 Roadmap

- [ ] Advanced scheduling system
- [ ] Machine learning for action optimization
- [ ] Browser extension integration
- [ ] Cloud synchronization
- [ ] Advanced analytics dashboard
- [ ] Plugin system for custom actions

---

**Built with ❤️ by enhancing the proven automation logic from the original facebot.py**
