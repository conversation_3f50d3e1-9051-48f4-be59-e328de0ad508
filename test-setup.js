#!/usr/bin/env node

const fs = require('fs').promises;
const path = require('path');
const DatabaseManager = require('./src/database/DatabaseManager');
const ProfileManager = require('./src/core/ProfileManager');
const FacebookBot = require('./src/core/FacebookBot');

class TestSetup {
    constructor() {
        this.dbManager = null;
        this.profileManager = null;
    }

    async run() {
        console.log('🧪 Testing FaceBot Multi Setup...\n');
        
        try {
            await this.testDatabaseConnection();
            await this.testProfileManager();
            await this.testFacebookBot();
            await this.createSampleData();
            
            console.log('\n✅ All tests passed! FaceBot Multi is ready to use.');
            console.log('\n📋 Next steps:');
            console.log('   1. Run "npm start" to launch the application');
            console.log('   2. Add your Facebook profiles in the Profiles tab');
            console.log('   3. Configure automation settings');
            console.log('   4. Start your first campaign!\n');
            
        } catch (error) {
            console.error('\n❌ Test failed:', error.message);
            console.error('Stack trace:', error.stack);
            process.exit(1);
        } finally {
            if (this.dbManager) {
                await this.dbManager.close();
            }
        }
    }

    async testDatabaseConnection() {
        console.log('🔍 Testing database connection...');
        
        this.dbManager = new DatabaseManager();
        await this.dbManager.initialize();
        
        // Test basic operations
        const settings = await this.dbManager.getSettings();
        console.log('   ✅ Database connected and initialized');
        console.log(`   ✅ Settings loaded: ${Object.keys(settings).length} entries`);
    }

    async testProfileManager() {
        console.log('\n👥 Testing profile manager...');
        
        this.profileManager = new ProfileManager(this.dbManager);
        await this.profileManager.initialize();
        
        // Test profile creation
        const testProfile = {
            name: 'Test Profile',
            email: '<EMAIL>',
            password: 'testpassword',
            facebook: {
                comments: ['Test comment 1', 'Test comment 2'],
                shares: ['Test share 1', 'Test share 2'],
                settings: {
                    delayComment: 4,
                    delayShare: 7,
                    enableComments: true,
                    enableShares: true
                }
            }
        };
        
        const createdProfile = await this.profileManager.createProfile(testProfile);
        console.log('   ✅ Profile creation works');
        
        // Test profile retrieval
        const retrievedProfile = await this.profileManager.getProfile(createdProfile.id);
        console.log('   ✅ Profile retrieval works');
        
        // Test profile validation
        const validation = await this.profileManager.validateProfile(createdProfile.id);
        console.log(`   ✅ Profile validation works: ${validation.valid}`);
        
        // Clean up test profile
        await this.profileManager.deleteProfile(createdProfile.id);
        console.log('   ✅ Profile deletion works');
    }

    async testFacebookBot() {
        console.log('\n🤖 Testing Facebook bot initialization...');
        
        const testProfile = {
            id: 'test-profile',
            name: 'Test Profile',
            email: '<EMAIL>',
            password: 'testpassword',
            userDataDir: path.join(process.cwd(), 'profiles', 'test-profile')
        };
        
        // Ensure test profile directory exists
        await fs.mkdir(testProfile.userDataDir, { recursive: true });
        
        const bot = new FacebookBot(testProfile, null, this.dbManager);
        
        try {
            // Test bot initialization (without actually launching browser)
            console.log('   ✅ FacebookBot class instantiation works');
            
            // Test utility methods
            await bot.randomDelay(100, 200);
            console.log('   ✅ Random delay method works');
            
            // Test health check method
            const isHealthy = await bot.isHealthy();
            console.log(`   ✅ Health check method works: ${isHealthy}`);
            
        } finally {
            await bot.cleanup();
            // Clean up test directory
            try {
                await fs.rmdir(testProfile.userDataDir, { recursive: true });
            } catch (error) {
                // Ignore cleanup errors
            }
        }
    }

    async createSampleData() {
        console.log('\n📝 Creating sample data...');
        
        // Create sample settings
        const sampleSettings = {
            'app.firstRun': 'false',
            'app.version': '1.0.0',
            'automation.defaultBatchSize': '3',
            'automation.defaultCooldown': '30'
        };
        
        await this.dbManager.updateSettings(sampleSettings);
        console.log('   ✅ Sample settings created');
        
        // Create sample proxy
        const sampleProxy = {
            name: 'Sample Proxy',
            server: '127.0.0.1:8080',
            username: 'user',
            password: 'pass',
            protocol: 'http',
            isEnabled: false
        };
        
        await this.dbManager.createProxy(sampleProxy);
        console.log('   ✅ Sample proxy created');
        
        // Create sample log entry
        await this.dbManager.addLog({
            profileId: 'system',
            action: 'system_test',
            target: 'setup',
            status: 'success',
            message: 'System test completed successfully'
        });
        console.log('   ✅ Sample log entry created');
    }

    async validateFileStructure() {
        console.log('\n📁 Validating file structure...');
        
        const requiredFiles = [
            'src/main.js',
            'src/preload.js',
            'src/core/FacebookBot.js',
            'src/core/ProfileManager.js',
            'src/core/AutomationController.js',
            'src/database/DatabaseManager.js',
            'src/renderer/index.html',
            'src/renderer/app.js',
            'package.json',
            'setup.js',
            'migrate.js'
        ];
        
        for (const file of requiredFiles) {
            try {
                await fs.access(file);
                console.log(`   ✅ ${file}`);
            } catch (error) {
                throw new Error(`Required file missing: ${file}`);
            }
        }
        
        const requiredDirs = [
            'src/core',
            'src/database',
            'src/renderer',
            'profiles',
            'data'
        ];
        
        for (const dir of requiredDirs) {
            try {
                await fs.access(dir);
                console.log(`   ✅ ${dir}/`);
            } catch (error) {
                throw new Error(`Required directory missing: ${dir}`);
            }
        }
    }

    async checkDependencies() {
        console.log('\n📦 Checking dependencies...');
        
        try {
            // Check if node_modules exists
            await fs.access('node_modules');
            console.log('   ✅ node_modules directory exists');
            
            // Check key dependencies
            const keyDeps = [
                'node_modules/electron',
                'node_modules/playwright',
                'node_modules/sqlite3',
                'node_modules/react',
                'node_modules/tailwindcss'
            ];
            
            for (const dep of keyDeps) {
                try {
                    await fs.access(dep);
                    console.log(`   ✅ ${path.basename(dep)} installed`);
                } catch (error) {
                    console.warn(`   ⚠️  ${path.basename(dep)} not found`);
                }
            }
            
        } catch (error) {
            throw new Error('Dependencies not installed. Run "npm install" first.');
        }
    }

    async performanceTest() {
        console.log('\n⚡ Running performance tests...');
        
        const startTime = Date.now();
        
        // Test database operations
        const dbStart = Date.now();
        for (let i = 0; i < 10; i++) {
            await this.dbManager.getSettings();
        }
        const dbTime = Date.now() - dbStart;
        console.log(`   ✅ Database operations: ${dbTime}ms for 10 queries`);
        
        // Test profile operations
        const profileStart = Date.now();
        const profiles = await this.profileManager.getAllProfiles();
        const profileTime = Date.now() - profileStart;
        console.log(`   ✅ Profile retrieval: ${profileTime}ms for ${profiles.length} profiles`);
        
        const totalTime = Date.now() - startTime;
        console.log(`   ✅ Total test time: ${totalTime}ms`);
    }
}

// Command line interface
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.includes('--help') || args.includes('-h')) {
        console.log(`
FaceBot Multi Test Suite

Usage:
  node test-setup.js                 # Run all tests
  node test-setup.js --help          # Show this help message

This tool will:
1. Test database connectivity and operations
2. Test profile manager functionality
3. Test Facebook bot initialization
4. Create sample data for testing
5. Validate file structure and dependencies
6. Run basic performance tests

All tests are non-destructive and safe to run multiple times.
        `);
        process.exit(0);
    }
    
    const test = new TestSetup();
    test.run().catch(error => {
        console.error('Test suite failed:', error);
        process.exit(1);
    });
}

module.exports = TestSetup;
