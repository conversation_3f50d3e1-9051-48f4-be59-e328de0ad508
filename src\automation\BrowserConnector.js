const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
// Use real WebSocket - no more mocks
const WebSocket = require('ws');
const fetch = require('node-fetch');

class BrowserConnector {
    constructor(profileManager, dbManager) {
        this.profileManager = profileManager;
        this.dbManager = dbManager;
        this.activeBrowsers = new Map(); // Map<profileId, { process, debugPort, ws }>
        this.baseDebugPort = 9222;
    }

    /**
     * Launch Chrome with debugging enabled for automation
     * @param {string} profileId - Profile ID
     * @param {object} options - Launch options
     * @returns {Promise<object>} Browser connection info
     */
    async launchBrowserForAutomation(profileId, options = {}) {
        try {
            const profile = this.profileManager.getProfile(profileId);
            if (!profile) {
                throw new Error('Profile not found');
            }

            console.log(`🚀 Launching Chrome for automation: ${profile.name}`);

            // Find available debug port
            const debugPort = await this.findAvailablePort();

            // Prepare Chrome arguments for automation
            const chromeArgs = [
                `--user-data-dir=${profile.userDataDir}`,
                `--remote-debugging-port=${debugPort}`,
                '--no-first-run',
                '--no-default-browser-check',
                '--disable-default-apps',
                '--disable-popup-blocking',
                '--disable-translate',
                '--disable-background-timer-throttling',
                '--disable-renderer-backgrounding',
                '--disable-backgrounding-occluded-windows',
                '--disable-ipc-flooding-protection',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--window-size=1200,800',
                '--window-position=100,100'
            ];

            // Add initial URL
            if (options.initialUrl) {
                chromeArgs.push(options.initialUrl);
            } else {
                chromeArgs.push('https://facebook.com');
            }

            // Find Chrome executable
            const chromePath = this.findChromeExecutable();
            if (!chromePath) {
                throw new Error('Chrome executable not found');
            }

            // Launch Chrome process
            const chromeProcess = spawn(chromePath, chromeArgs, {
                detached: false,
                stdio: 'pipe'
            });

            // Wait for Chrome to start and debugging to be available
            await this.waitForDebugger(debugPort);

            // Connect to Chrome DevTools
            const wsConnection = await this.connectToDevTools(debugPort);

            // Store browser info
            this.activeBrowsers.set(profileId, {
                process: chromeProcess,
                debugPort: debugPort,
                ws: wsConnection,
                profileId: profileId,
                startTime: Date.now()
            });

            console.log(`✅ Chrome launched for automation: ${profile.name} (Debug port: ${debugPort})`);

            return {
                success: true,
                profileId: profileId,
                debugPort: debugPort,
                wsConnection: wsConnection
            };

        } catch (error) {
            console.error(`❌ Failed to launch Chrome for automation:`, error.message);
            return { success: false, error: error.message };
        }
    }

    /**
     * Execute JavaScript in browser
     * @param {string} profileId - Profile ID
     * @param {string} script - JavaScript code to execute
     * @returns {Promise<object>} Execution result
     */
    async executeScript(profileId, script) {
        try {
            const browser = this.activeBrowsers.get(profileId);
            if (!browser || !browser.ws) {
                throw new Error('Browser not connected for profile');
            }

            const result = await this.sendDevToolsCommand(browser.ws, 'Runtime.evaluate', {
                expression: script,
                returnByValue: true,
                awaitPromise: true
            });

            return { success: true, result: result.result };
        } catch (error) {
            console.error(`Script execution failed for ${profileId}:`, error.message);
            return { success: false, error: error.message };
        }
    }

    /**
     * Navigate to URL
     * @param {string} profileId - Profile ID
     * @param {string} url - URL to navigate to
     * @returns {Promise<object>} Navigation result
     */
    async navigateToUrl(profileId, url) {
        try {
            const browser = this.activeBrowsers.get(profileId);
            if (!browser || !browser.ws) {
                throw new Error('Browser not connected for profile');
            }

            await this.sendDevToolsCommand(browser.ws, 'Page.navigate', { url });

            // Wait for page load
            await this.waitForPageLoad(browser.ws);

            console.log(`🔗 Navigated to: ${url}`);
            return { success: true };
        } catch (error) {
            console.error(`Navigation failed for ${profileId}:`, error.message);
            return { success: false, error: error.message };
        }
    }

    /**
     * Click element using selector
     * @param {string} profileId - Profile ID
     * @param {string} selector - CSS selector
     * @returns {Promise<object>} Click result
     */
    async clickElement(profileId, selector) {
        try {
            const script = `
                (function() {
                    const element = document.querySelector('${selector}');
                    if (element) {
                        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        setTimeout(() => {
                            element.click();
                        }, 500);
                        return { success: true, found: true };
                    } else {
                        return { success: false, found: false, error: 'Element not found' };
                    }
                })()
            `;

            const result = await this.executeScript(profileId, script);
            return result;
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * Type text into element
     * @param {string} profileId - Profile ID
     * @param {string} selector - CSS selector
     * @param {string} text - Text to type
     * @returns {Promise<object>} Type result
     */
    async typeText(profileId, selector, text) {
        try {
            const script = `
                (function() {
                    const element = document.querySelector('${selector}');
                    if (element) {
                        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        element.focus();
                        element.value = '${text.replace(/'/g, "\\'")}';
                        element.dispatchEvent(new Event('input', { bubbles: true }));
                        element.dispatchEvent(new Event('change', { bubbles: true }));
                        return { success: true, found: true };
                    } else {
                        return { success: false, found: false, error: 'Element not found' };
                    }
                })()
            `;

            const result = await this.executeScript(profileId, script);
            return result;
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * Wait for element to appear
     * @param {string} profileId - Profile ID
     * @param {string} selector - CSS selector
     * @param {number} timeout - Timeout in milliseconds
     * @returns {Promise<object>} Wait result
     */
    async waitForElement(profileId, selector, timeout = 10000) {
        try {
            const script = `
                (function() {
                    return new Promise((resolve) => {
                        const startTime = Date.now();
                        const checkElement = () => {
                            const element = document.querySelector('${selector}');
                            if (element) {
                                resolve({ success: true, found: true });
                            } else if (Date.now() - startTime > ${timeout}) {
                                resolve({ success: false, found: false, error: 'Timeout waiting for element' });
                            } else {
                                setTimeout(checkElement, 100);
                            }
                        };
                        checkElement();
                    });
                })()
            `;

            const result = await this.executeScript(profileId, script);
            return result;
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * Close browser for profile
     * @param {string} profileId - Profile ID
     * @returns {Promise<boolean>} Success status
     */
    async closeBrowser(profileId) {
        try {
            const browser = this.activeBrowsers.get(profileId);
            if (!browser) {
                return false;
            }

            // Close WebSocket connection
            if (browser.ws) {
                browser.ws.close();
            }

            // Kill Chrome process
            if (browser.process && !browser.process.killed) {
                browser.process.kill('SIGTERM');
            }

            this.activeBrowsers.delete(profileId);
            console.log(`✅ Browser closed for profile: ${profileId}`);
            return true;
        } catch (error) {
            console.error(`Failed to close browser for ${profileId}:`, error.message);
            return false;
        }
    }

    /**
     * Find available port for debugging
     * @returns {Promise<number>} Available port
     */
    async findAvailablePort() {
        for (let port = this.baseDebugPort; port < this.baseDebugPort + 100; port++) {
            if (!this.isPortInUse(port)) {
                return port;
            }
        }
        throw new Error('No available debug ports');
    }

    /**
     * Check if port is in use
     * @param {number} port - Port to check
     * @returns {boolean} Port in use status
     */
    isPortInUse(port) {
        return Array.from(this.activeBrowsers.values()).some(browser => browser.debugPort === port);
    }

    /**
     * Find Chrome executable
     * @returns {string|null} Chrome path
     */
    findChromeExecutable() {
        const chromePaths = [
            'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
            'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe',
            process.env.CHROME_PATH,
            'google-chrome',
            'chrome'
        ].filter(Boolean);

        for (const chromePath of chromePaths) {
            if (fs.existsSync(chromePath)) {
                return chromePath;
            }
        }
        return null;
    }

    /**
     * Wait for Chrome debugger to be ready
     * @param {number} port - Debug port
     * @returns {Promise<void>}
     */
    async waitForDebugger(port) {
        const maxAttempts = 30;
        for (let i = 0; i < maxAttempts; i++) {
            try {
                const response = await fetch(`http://localhost:${port}/json/version`);
                if (response.ok) {
                    return;
                }
            } catch (error) {
                // Continue waiting
            }
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        throw new Error('Chrome debugger not ready');
    }

    /**
     * Connect to Chrome DevTools WebSocket
     * @param {number} port - Debug port
     * @returns {Promise<WebSocket>} WebSocket connection
     */
    async connectToDevTools(port) {
        try {
            const response = await fetch(`http://localhost:${port}/json`);
            const tabs = await response.json();
            const tab = tabs[0]; // Use first tab

            const ws = new WebSocket(tab.webSocketDebuggerUrl);

            return new Promise((resolve, reject) => {
                ws.on('open', () => {
                    console.log(`🔗 Connected to Chrome DevTools on port ${port}`);
                    resolve(ws);
                });

                ws.on('error', reject);
            });
        } catch (error) {
            throw new Error(`Failed to connect to DevTools: ${error.message}`);
        }
    }

    /**
     * Send command to Chrome DevTools
     * @param {WebSocket} ws - WebSocket connection
     * @param {string} method - DevTools method
     * @param {object} params - Method parameters
     * @returns {Promise<object>} Command result
     */
    async sendDevToolsCommand(ws, method, params = {}) {
        return new Promise((resolve, reject) => {
            const id = Date.now();
            const command = { id, method, params };

            const timeout = setTimeout(() => {
                reject(new Error('DevTools command timeout'));
            }, 10000);

            const messageHandler = (data) => {
                const response = JSON.parse(data);
                if (response.id === id) {
                    clearTimeout(timeout);
                    ws.removeListener('message', messageHandler);

                    if (response.error) {
                        reject(new Error(response.error.message));
                    } else {
                        resolve(response.result);
                    }
                }
            };

            ws.on('message', messageHandler);
            ws.send(JSON.stringify(command));
        });
    }

    /**
     * Wait for page to load
     * @param {WebSocket} ws - WebSocket connection
     * @returns {Promise<void>}
     */
    async waitForPageLoad(ws) {
        await this.sendDevToolsCommand(ws, 'Page.enable');

        return new Promise((resolve) => {
            const messageHandler = (data) => {
                const response = JSON.parse(data);
                if (response.method === 'Page.loadEventFired') {
                    ws.removeListener('message', messageHandler);
                    resolve();
                }
            };

            ws.on('message', messageHandler);
        });
    }

    /**
     * Get all active browsers
     * @returns {Array} Array of active browser info
     */
    getActiveBrowsers() {
        return Array.from(this.activeBrowsers.entries()).map(([profileId, browser]) => ({
            profileId,
            debugPort: browser.debugPort,
            startTime: browser.startTime,
            uptime: Date.now() - browser.startTime
        }));
    }

    /**
     * Cleanup all browsers
     * @returns {Promise<void>}
     */
    async cleanup() {
        console.log('🧹 Cleaning up browser connections...');

        const profileIds = Array.from(this.activeBrowsers.keys());
        await Promise.all(profileIds.map(profileId => this.closeBrowser(profileId)));

        console.log('✅ Browser connector cleanup complete');
    }
}

module.exports = BrowserConnector;
