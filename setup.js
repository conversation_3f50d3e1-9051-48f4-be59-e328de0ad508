#!/usr/bin/env node

const fs = require('fs').promises;
const path = require('path');
const { execSync } = require('child_process');

class FaceBotSetup {
    constructor() {
        this.projectRoot = process.cwd();
        this.requiredDirs = [
            'src/core',
            'src/database', 
            'src/renderer/components',
            'src/renderer/pages',
            'src/renderer/styles',
            'profiles',
            'data',
            'assets'
        ];
    }

    async run() {
        console.log('🚀 Setting up FaceBot Multi...\n');
        
        try {
            await this.checkNodeVersion();
            await this.createDirectories();
            await this.installDependencies();
            await this.setupPlaywright();
            await this.createDataDirectory();
            await this.createAssets();
            await this.validateSetup();
            
            console.log('\n✅ Setup completed successfully!');
            console.log('\n📋 Next steps:');
            console.log('   1. Run "npm start" to launch the application');
            console.log('   2. Create your first profile in the Profiles tab');
            console.log('   3. Configure automation settings');
            console.log('   4. Start your first campaign!\n');
            
        } catch (error) {
            console.error('\n❌ Setup failed:', error.message);
            process.exit(1);
        }
    }

    async checkNodeVersion() {
        console.log('🔍 Checking Node.js version...');
        
        const nodeVersion = process.version;
        const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
        
        if (majorVersion < 16) {
            throw new Error(`Node.js 16 or higher is required. Current version: ${nodeVersion}`);
        }
        
        console.log(`   ✅ Node.js ${nodeVersion} is compatible`);
    }

    async createDirectories() {
        console.log('\n📁 Creating project directories...');
        
        for (const dir of this.requiredDirs) {
            const fullPath = path.join(this.projectRoot, dir);
            try {
                await fs.mkdir(fullPath, { recursive: true });
                console.log(`   ✅ Created: ${dir}`);
            } catch (error) {
                if (error.code !== 'EEXIST') {
                    throw new Error(`Failed to create directory ${dir}: ${error.message}`);
                }
                console.log(`   ℹ️  Exists: ${dir}`);
            }
        }
    }

    async installDependencies() {
        console.log('\n📦 Installing dependencies...');
        
        try {
            console.log('   Installing npm packages...');
            execSync('npm install', { 
                stdio: 'inherit',
                cwd: this.projectRoot 
            });
            console.log('   ✅ Dependencies installed successfully');
        } catch (error) {
            throw new Error(`Failed to install dependencies: ${error.message}`);
        }
    }

    async setupPlaywright() {
        console.log('\n🎭 Setting up Playwright...');
        
        try {
            console.log('   Installing Playwright browsers...');
            execSync('npx playwright install chromium', { 
                stdio: 'inherit',
                cwd: this.projectRoot 
            });
            console.log('   ✅ Playwright browsers installed');
        } catch (error) {
            console.warn('   ⚠️  Playwright installation failed, you may need to run "npx playwright install chromium" manually');
        }
    }

    async createDataDirectory() {
        console.log('\n💾 Setting up data directory...');
        
        const dataDir = path.join(this.projectRoot, 'data');
        try {
            await fs.mkdir(dataDir, { recursive: true });
            
            // Create .gitkeep file to ensure directory is tracked
            await fs.writeFile(path.join(dataDir, '.gitkeep'), '');
            console.log('   ✅ Data directory created');
        } catch (error) {
            throw new Error(`Failed to create data directory: ${error.message}`);
        }
    }

    async createAssets() {
        console.log('\n🎨 Creating default assets...');
        
        const assetsDir = path.join(this.projectRoot, 'assets');
        
        // Create a simple icon placeholder
        const iconSvg = `
<svg width="256" height="256" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
        </linearGradient>
    </defs>
    <rect width="256" height="256" rx="32" fill="url(#grad1)"/>
    <text x="128" y="140" font-family="Arial, sans-serif" font-size="120" font-weight="bold" text-anchor="middle" fill="white">FB</text>
    <text x="128" y="200" font-family="Arial, sans-serif" font-size="24" text-anchor="middle" fill="white">Multi</text>
</svg>`;
        
        try {
            await fs.writeFile(path.join(assetsDir, 'icon.svg'), iconSvg.trim());
            console.log('   ✅ Default icon created');
        } catch (error) {
            console.warn('   ⚠️  Failed to create default assets');
        }
    }

    async validateSetup() {
        console.log('\n🔍 Validating setup...');
        
        // Check if main files exist
        const requiredFiles = [
            'src/main.js',
            'src/preload.js',
            'src/core/FacebookBot.js',
            'src/core/ProfileManager.js',
            'src/core/AutomationController.js',
            'src/database/DatabaseManager.js',
            'src/renderer/index.html',
            'src/renderer/app.js',
            'package.json'
        ];
        
        for (const file of requiredFiles) {
            const filePath = path.join(this.projectRoot, file);
            try {
                await fs.access(filePath);
                console.log(`   ✅ ${file}`);
            } catch (error) {
                throw new Error(`Required file missing: ${file}`);
            }
        }
        
        // Check if node_modules exists
        try {
            await fs.access(path.join(this.projectRoot, 'node_modules'));
            console.log('   ✅ node_modules directory');
        } catch (error) {
            throw new Error('node_modules directory not found. Dependencies may not be installed correctly.');
        }
    }
}

// Run setup if this file is executed directly
if (require.main === module) {
    const setup = new FaceBotSetup();
    setup.run().catch(error => {
        console.error('Setup failed:', error);
        process.exit(1);
    });
}

module.exports = FaceBotSetup;
