<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FaceBot Multi - Facebook Automation</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .sidebar-item {
            transition: all 0.3s ease;
        }

        .sidebar-item:hover {
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }

        .sidebar-item.active {
            background-color: rgba(255, 255, 255, 0.2);
            border-right: 4px solid #fff;
        }

        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }

        .status-active { background-color: #10b981; }
        .status-inactive { background-color: #ef4444; }
        .status-cooldown { background-color: #f59e0b; }
        .status-running { background-color: #3b82f6; }

        .progress-bar {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            transition: width 0.3s ease;
        }

        .loading-spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Category Filter Styles */
        .category-filter-container {
            position: relative;
            display: inline-block;
            margin-right: 8px;
            margin-bottom: 8px;
        }

        .category-filter {
            padding: 8px 16px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            background-color: white;
            color: #374151;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
        }

        .category-filter:hover {
            background-color: #f9fafb;
            border-color: #9ca3af;
        }

        .category-filter.active {
            background-color: #eff6ff;
            border-color: #3b82f6;
            color: #1d4ed8;
        }

        .category-delete-btn {
            position: absolute;
            top: -4px;
            right: -4px;
            width: 18px;
            height: 18px;
            background-color: #ef4444;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            cursor: pointer;
            transition: all 0.2s ease;
            opacity: 0;
            border: none;
        }

        .category-delete-btn:hover {
            background-color: #dc2626;
            transform: scale(1.1);
        }

        .category-filter-container:hover .category-delete-btn {
            opacity: 1;
        }

        /* Modal Styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 50;
        }

        .modal-content {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            max-width: 28rem;
            width: 100%;
            margin: 1rem;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .modal-header h3 {
            font-size: 1.125rem;
            font-weight: 600;
            margin: 0;
        }

        .modal-close {
            color: #9ca3af;
            font-size: 1.25rem;
            cursor: pointer;
            background: none;
            border: none;
            padding: 0;
        }

        .modal-close:hover {
            color: #6b7280;
        }

        .modal-body {
            padding: 1.5rem;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 0.75rem;
            padding: 1.5rem;
            border-top: 1px solid #e5e7eb;
            background-color: #f9fafb;
            flex-shrink: 0;
        }

        .btn-secondary {
            padding: 0.5rem 1rem;
            color: #374151;
            background-color: #e5e7eb;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }

        .btn-secondary:hover {
            background-color: #d1d5db;
        }

        /* Ensure modal buttons are visible and properly styled */
        .btn-primary {
            padding: 0.5rem 1rem !important;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
            border: none !important;
            border-radius: 6px !important;
            cursor: pointer !important;
            font-size: 14px !important;
            display: inline-flex !important;
            align-items: center !important;
            transition: all 0.3s ease !important;
            text-decoration: none !important;
        }

        .btn-primary:hover {
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4) !important;
        }

        /* Ensure modal content is properly sized */
        .modal-content {
            max-height: 90vh !important;
            overflow: hidden !important;
            display: flex !important;
            flex-direction: column !important;
            background: white !important;
            border-radius: 8px !important;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
        }

        .modal-body {
            overflow-y: auto !important;
            flex: 1 !important;
        }

        .modal-footer {
            background-color: #f9fafb !important;
            border-top: 1px solid #e5e7eb !important;
            padding: 1.5rem !important;
            display: flex !important;
            justify-content: flex-end !important;
            gap: 0.75rem !important;
            flex-shrink: 0 !important;
            position: relative !important;
            z-index: 1000 !important;
        }

        /* Force button visibility */
        .modal-footer button {
            display: inline-flex !important;
            align-items: center !important;
            padding: 0.5rem 1rem !important;
            border-radius: 6px !important;
            font-size: 14px !important;
            cursor: pointer !important;
            transition: all 0.3s ease !important;
            border: none !important;
        }

        .modal-footer .btn-secondary {
            background-color: #6b7280 !important;
            color: white !important;
        }

        .modal-footer .btn-secondary:hover {
            background-color: #4b5563 !important;
        }

        /* Ensure modal is properly sized and visible */
        .modal-overlay {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            background-color: rgba(0, 0, 0, 0.5) !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            z-index: 9999 !important;
        }

        /* Force modal content to be visible */
        .modal-content {
            background: white !important;
            border-radius: 8px !important;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
            width: 90% !important;
            max-width: 800px !important;
            max-height: 90vh !important;
            overflow: hidden !important;
            display: flex !important;
            flex-direction: column !important;
        }

        /* Ensure form takes full height */
        .modal-content form {
            display: flex !important;
            flex-direction: column !important;
            height: 100% !important;
        }

        /* Force modal body to be scrollable and footer to be visible */
        .modal-body {
            flex: 1 !important;
            overflow-y: auto !important;
            padding: 1.5rem !important;
            max-height: calc(90vh - 120px) !important;
        }

        /* Ensure footer is always visible at bottom */
        .modal-footer {
            margin-top: auto !important;
            flex-shrink: 0 !important;
            border-top: 1px solid #e5e7eb !important;
            background: white !important;
        }

        /* Button styling for better visibility */
        .btn-primary {
            background-color: #3b82f6 !important;
            color: white !important;
            padding: 0.5rem 1rem !important;
            border-radius: 6px !important;
            border: none !important;
            cursor: pointer !important;
            font-size: 14px !important;
        }

        .btn-primary:hover {
            background-color: #2563eb !important;
        }

        .btn-secondary {
            background-color: #6b7280 !important;
            color: white !important;
            padding: 0.5rem 1rem !important;
            border-radius: 6px !important;
            border: none !important;
            cursor: pointer !important;
            font-size: 14px !important;
        }

        .btn-secondary:hover {
            background-color: #4b5563 !important;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div id="app" class="flex h-screen">
        <!-- Sidebar -->
        <div class="sidebar w-64 text-white p-6">
            <div class="mb-8">
                <h1 class="text-2xl font-bold flex items-center">
                    <i class="fab fa-facebook-f mr-3"></i>
                    FaceBot Multi
                </h1>
                <p class="text-sm opacity-75 mt-1">Facebook Automation Suite</p>
            </div>

            <nav class="space-y-2">
                <a href="#" class="sidebar-item active flex items-center p-3 rounded-lg" data-page="dashboard">
                    <i class="fas fa-tachometer-alt mr-3"></i>
                    Dashboard
                </a>
                <a href="#" class="sidebar-item flex items-center p-3 rounded-lg" data-page="profiles">
                    <i class="fas fa-users mr-3"></i>
                    Profiles
                </a>
                <a href="#" class="sidebar-item flex items-center p-3 rounded-lg" data-page="automation">
                    <i class="fas fa-robot mr-3"></i>
                    Automation
                </a>
                <a href="#" class="sidebar-item flex items-center p-3 rounded-lg" data-page="scheduling">
                    <i class="fas fa-calendar-alt mr-3"></i>
                    Scheduling
                </a>
                <a href="#" class="sidebar-item flex items-center p-3 rounded-lg" data-page="proxies">
                    <i class="fas fa-network-wired mr-3"></i>
                    Proxies
                </a>
                <a href="#" class="sidebar-item flex items-center p-3 rounded-lg" data-page="logs">
                    <i class="fas fa-list-alt mr-3"></i>
                    Logs
                </a>
                <a href="#" class="sidebar-item flex items-center p-3 rounded-lg" data-page="settings">
                    <i class="fas fa-cog mr-3"></i>
                    Settings
                </a>
            </nav>

            <div class="mt-auto pt-8">
                <div class="bg-white bg-opacity-10 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm">System Status</span>
                        <span class="status-indicator status-active"></span>
                    </div>
                    <div class="text-xs opacity-75">
                        <div>Profiles: <span id="profile-count">0</span></div>
                        <div>Active: <span id="active-campaigns">0</span></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 id="page-title" class="text-2xl font-semibold text-gray-800">Dashboard</h2>
                        <p id="page-subtitle" class="text-gray-600">Overview of your automation activities</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button id="refresh-btn" class="p-2 text-gray-500 hover:text-gray-700 rounded-lg hover:bg-gray-100">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <div class="flex items-center space-x-2 text-sm text-gray-500">
                            <i class="fas fa-clock"></i>
                            <span id="current-time"></span>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <main class="flex-1 p-6 overflow-auto">
                <div id="page-content" class="fade-in">
                    <!-- Dashboard content will be loaded here -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <!-- Stats Cards -->
                        <div class="card p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-blue-100 text-blue-600 mr-4">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Total Profiles</p>
                                    <p class="text-2xl font-semibold" id="total-profiles">0</p>
                                </div>
                            </div>
                        </div>

                        <div class="card p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-green-100 text-green-600 mr-4">
                                    <i class="fas fa-play"></i>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Active Campaigns</p>
                                    <p class="text-2xl font-semibold" id="active-campaigns-count">0</p>
                                </div>
                            </div>
                        </div>

                        <div class="card p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600 mr-4">
                                    <i class="fas fa-check"></i>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Success Rate</p>
                                    <p class="text-2xl font-semibold" id="success-rate">0%</p>
                                </div>
                            </div>
                        </div>

                        <div class="card p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-purple-100 text-purple-600 mr-4">
                                    <i class="fas fa-network-wired"></i>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Proxies</p>
                                    <p class="text-2xl font-semibold" id="total-proxies">0</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="card p-6">
                        <h3 class="text-lg font-semibold mb-4">Recent Activity</h3>
                        <div id="recent-activity" class="space-y-3">
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center">
                                    <span class="status-indicator status-active"></span>
                                    <span class="text-sm">System initialized successfully</span>
                                </div>
                                <span class="text-xs text-gray-500" id="init-time"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-6 flex items-center space-x-4">
            <div class="loading-spinner"></div>
            <span id="loading-text">Loading...</span>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toast-container" class="fixed top-4 right-4 space-y-2 z-40"></div>

    <!-- Modal Container -->
    <div id="modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div id="modal-content" class="bg-white rounded-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden flex flex-col">
            <!-- Modal content will be inserted here -->
        </div>
    </div>

    <!-- Application JavaScript -->
    <script src="app.js"></script>
</body>
</html>
