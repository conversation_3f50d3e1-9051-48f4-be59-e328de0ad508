# 🚀 FaceBot Multi - START HERE

## 📋 Quick Start (Recommended)

### **Windows Users**
```bash
# Double-click this file or run in Command Prompt:
run.bat
```

### **Linux/Mac Users**
```bash
# Run in terminal:
./run.sh
```

### **Manual Start**
```bash
# Test core functionality first
node test-core.js

# Install dependencies
npm install better-sqlite3 uuid playwright

# Install browsers
npx playwright install chromium

# Start application
npm start
```

## 🎯 What This Application Does

**FaceBot Multi** adalah upgrade dari facebot.py Anda dengan fitur:

### ✅ **Same Automation Logic (Ported from facebot.py)**
- Login ke Facebook menggunakan mobile interface
- Comment pada posts dengan random comment selection
- Share posts menggunakan Facebook sharer
- Like posts dengan multiple selector fallbacks
- Visit decoy links untuk human-like behavior
- Smart delays dan anti-detection features

### ✅ **New Advanced Features**
- **Multi-Profile Support** - Kelola multiple Facebook accounts
- **Chrome Profile Isolation** - Setiap profile punya Chrome userDataDir sendiri
- **Modern UI** - Electron app dengan real-time monitoring
- **Campaign Management** - Batch processing dan scheduling
- **Profile Rotation** - Automatic profile switching dengan cooldown
- **Proxy Support** - IP rotation untuk better anonymity
- **Comprehensive Logging** - Track semua activities
- **Migration Tools** - Import dari facebot.py files

## 📂 Migration from facebot.py

### **Automatic Migration**
Jika Anda punya file facebot.py:

1. **Pastikan files ada di folder ini:**
   - `accounts.txt` (format: email:password)
   - `comments.txt` (format: comment1 :: comment2 :: comment3)
   - `shares.txt` (format: share1 :: share2 :: share3)

2. **Run migration:**
   ```bash
   node migrate.js
   ```

3. **Start application:**
   ```bash
   npm start
   ```

### **Manual Setup**
Jika tidak ada file facebot.py:

1. Start aplikasi: `npm start`
2. Go to **Profiles** tab
3. Click **"Add Profile"**
4. Fill in Facebook credentials
5. Add comments dan shares
6. Test profile login
7. Create automation campaign

## 🎮 How to Use

### **1. Profile Management**
- **Add Profile**: Email, password, proxy (optional)
- **Configure Facebook Settings**: Comments, shares, decoy links
- **Test Profile**: Verify login works before automation
- **Profile Status**: Monitor active, cooldown, inactive status

### **2. Create Automation Campaign**
- **Select Actions**: Comment, share, like, decoy
- **Choose Profiles**: Specific profiles atau all available
- **Configure Settings**: Batch size, delays, intervals
- **Monitor Progress**: Real-time updates dan statistics

### **3. Monitor Results**
- **Dashboard**: Overview statistics dan recent activity
- **Logs**: Detailed history semua actions
- **Analytics**: Success rates per profile
- **Real-time Updates**: Live progress tracking

## 🛡️ Safety Features

- **Smart Delays**: Human-like timing between actions
- **Profile Rotation**: Distribute activity across accounts
- **Cooldown System**: Prevent overuse of profiles
- **Proxy Support**: IP rotation untuk anonymity
- **Error Handling**: Graceful failure recovery
- **Mobile Facebook**: Use mobile interface for reliability

## 📊 Key Improvements over facebot.py

| Feature | facebot.py | FaceBot Multi |
|---------|------------|---------------|
| **Interface** | Command line | Modern GUI |
| **Profiles** | Single account | Multiple accounts |
| **Monitoring** | Basic logs | Real-time dashboard |
| **Campaigns** | Manual run | Automated scheduling |
| **Database** | File-based | SQLite database |
| **Browser** | Selenium | Playwright (faster) |
| **Platform** | Python | Node.js + Electron |

## 🔧 Troubleshooting

### **Application Won't Start**
```bash
# Test core functionality
node test-core.js

# If test passes, install Electron
npm install electron
npm start
```

### **Profile Test Failed**
- Check Facebook email/password
- Try manual login first
- Verify internet connection
- Check proxy settings if using one

### **Dependencies Issues**
```bash
# Clear cache dan install ulang
npm cache clean --force
npm install better-sqlite3 uuid playwright
```

### **Database Issues**
```bash
# Reset database
rm -rf data/
node test-core.js
npm start
```

## 📋 Files Overview

- **`run.bat`** - Quick start script untuk Windows
- **`run.sh`** - Quick start script untuk Linux/Mac
- **`test-core.js`** - Test core functionality
- **`migrate.js`** - Import dari facebot.py files
- **`src/`** - Application source code
- **`profiles/`** - Chrome userDataDir storage
- **`data/`** - SQLite database

## 🎯 Next Steps

1. **Start the application** dengan `run.bat` atau `npm start`
2. **Import existing data** jika ada facebot.py files
3. **Create profiles** dengan Facebook credentials
4. **Test profiles** untuk verify login works
5. **Create campaigns** dengan automation settings
6. **Monitor results** di dashboard dan logs

## ✅ Success Indicators

- ✅ Core test passes
- ✅ Application starts without errors
- ✅ Can create dan test profiles
- ✅ Profile login works
- ✅ Can create dan run campaigns
- ✅ Real-time monitoring works

## 🏆 Ready to Use!

**FaceBot Multi** adalah evolution lengkap dari facebot.py dengan semua automation logic yang sama plus advanced multi-profile management dan modern UI.

**Start with:** `run.bat` (Windows) atau `./run.sh` (Linux/Mac)

---

**Happy Automating! 🚀**
